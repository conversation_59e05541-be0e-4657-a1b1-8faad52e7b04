-- PetTime Capsule Database Schema for Supabase
-- Complete schema for $2M/month revenue platform

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- USERS & AUTHENTICATION
-- =============================================

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    display_name TEXT NOT NULL,
    avatar_url TEXT,
    subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'premium', 'family', 'professional')),
    subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'expired')),
    subscription_expires_at TIMESTAMPTZ,
    total_revenue DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- PETS & PROFILES
-- =============================================

-- Pets table
CREATE TABLE public.pets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    owner_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    species TEXT NOT NULL CHECK (species IN ('dog', 'cat', 'bird', 'rabbit', 'hamster', 'fish', 'reptile', 'other')),
    breed TEXT,
    birth_date DATE,
    adoption_date DATE,
    gender TEXT CHECK (gender IN ('male', 'female', 'unknown')),
    weight DECIMAL(5,2),
    color TEXT,
    microchip_id TEXT,
    is_memorial BOOLEAN DEFAULT FALSE,
    memorial_date DATE,
    profile_image_url TEXT,
    personality_traits JSONB DEFAULT '[]',
    health_conditions JSONB DEFAULT '[]',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- MEMORIES & CONTENT
-- =============================================

-- Memories table
CREATE TABLE public.memories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pet_id UUID NOT NULL REFERENCES public.pets(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    memory_type TEXT NOT NULL CHECK (memory_type IN ('photo', 'video', 'audio', 'text', 'milestone', 'health', 'achievement')),
    content_url TEXT,
    thumbnail_url TEXT,
    ai_analysis JSONB,
    sentiment_score DECIMAL(3,2),
    milestone_type TEXT,
    tags JSONB DEFAULT '[]',
    location JSONB,
    is_favorite BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Memory reactions (likes, hearts, etc.)
CREATE TABLE public.memory_reactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    memory_id UUID NOT NULL REFERENCES public.memories(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    reaction_type TEXT NOT NULL CHECK (reaction_type IN ('like', 'love', 'laugh', 'sad', 'wow')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(memory_id, user_id, reaction_type)
);

-- =============================================
-- AI & VIDEO SERVICES
-- =============================================

-- AI prompts and suggestions
CREATE TABLE public.ai_prompts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pet_id UUID NOT NULL REFERENCES public.pets(id) ON DELETE CASCADE,
    prompt_type TEXT NOT NULL CHECK (prompt_type IN ('memory', 'milestone', 'health', 'behavior', 'photo')),
    prompt_text TEXT NOT NULL,
    ai_confidence DECIMAL(3,2),
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Video montages
CREATE TABLE public.video_montages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pet_id UUID NOT NULL REFERENCES public.pets(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    theme TEXT NOT NULL,
    duration INTEGER, -- in seconds
    video_url TEXT,
    thumbnail_url TEXT,
    memory_ids JSONB DEFAULT '[]',
    ai_script JSONB,
    processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
    view_count INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- SOCIAL NETWORK
-- =============================================

-- Pet connections/friendships
CREATE TABLE public.pet_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_pet_id UUID NOT NULL REFERENCES public.pets(id) ON DELETE CASCADE,
    requested_pet_id UUID NOT NULL REFERENCES public.pets(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'blocked')),
    message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(requester_pet_id, requested_pet_id)
);

-- Community posts
CREATE TABLE public.community_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    post_type TEXT DEFAULT 'text' CHECK (post_type IN ('text', 'photo', 'video', 'memory_share', 'milestone')),
    media_url TEXT,
    memory_id UUID REFERENCES public.memories(id) ON DELETE SET NULL,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Post comments
CREATE TABLE public.post_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID NOT NULL REFERENCES public.community_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    parent_comment_id UUID REFERENCES public.post_comments(id) ON DELETE CASCADE,
    like_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- MEMORIAL SYSTEM
-- =============================================

-- Memorial gardens
CREATE TABLE public.memorial_gardens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pet_id UUID NOT NULL REFERENCES public.pets(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    theme TEXT NOT NULL CHECK (theme IN ('peaceful', 'rainbow', 'garden', 'heroic', 'joyful', 'elegant')),
    background_url TEXT,
    memorial_text TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    visit_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Virtual flowers in memorial gardens
CREATE TABLE public.memorial_flowers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    garden_id UUID NOT NULL REFERENCES public.memorial_gardens(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    flower_type TEXT NOT NULL CHECK (flower_type IN ('rose', 'lily', 'tulip', 'sunflower', 'daisy', 'carnation')),
    flower_color TEXT NOT NULL CHECK (flower_color IN ('red', 'white', 'yellow', 'pink', 'purple', 'blue')),
    message TEXT,
    position_x DECIMAL(5,2),
    position_y DECIMAL(5,2),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Memorial tributes
CREATE TABLE public.memorial_tributes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pet_id UUID NOT NULL REFERENCES public.pets(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    title TEXT,
    message TEXT NOT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    is_anonymous BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- MARKETPLACE & REVENUE
-- =============================================

-- Marketplace products
CREATE TABLE public.marketplace_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    seller_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL CHECK (category IN ('memory_books', 'custom_portraits', 'memorial_items', 'pet_care', 'accessories', 'services')),
    price DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(3,2) DEFAULT 0.30,
    image_urls JSONB DEFAULT '[]',
    is_digital BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    sales_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Marketplace orders
CREATE TABLE public.marketplace_orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    buyer_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.marketplace_products(id) ON DELETE CASCADE,
    seller_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    quantity INTEGER DEFAULT 1,
    total_amount DECIMAL(10,2) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'processing', 'shipped', 'delivered', 'cancelled')),
    payment_intent_id TEXT,
    shipping_address JSONB,
    tracking_number TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Subscription transactions
CREATE TABLE public.subscription_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    plan_id TEXT NOT NULL,
    plan_name TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    stripe_payment_intent_id TEXT,
    billing_period_start TIMESTAMPTZ,
    billing_period_end TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- ANALYTICS & METRICS
-- =============================================

-- User analytics events
CREATE TABLE public.analytics_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    event_type TEXT NOT NULL,
    event_properties JSONB DEFAULT '{}',
    session_id TEXT,
    device_info JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Revenue metrics (aggregated daily)
CREATE TABLE public.revenue_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    subscription_revenue DECIMAL(12,2) DEFAULT 0,
    marketplace_revenue DECIMAL(12,2) DEFAULT 0,
    ai_services_revenue DECIMAL(12,2) DEFAULT 0,
    total_revenue DECIMAL(12,2) DEFAULT 0,
    new_subscribers INTEGER DEFAULT 0,
    churned_subscribers INTEGER DEFAULT 0,
    active_subscribers INTEGER DEFAULT 0,
    marketplace_orders INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(date)
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- User indexes
CREATE INDEX idx_users_subscription_tier ON public.users(subscription_tier);
CREATE INDEX idx_users_created_at ON public.users(created_at);

-- Pet indexes
CREATE INDEX idx_pets_owner_id ON public.pets(owner_id);
CREATE INDEX idx_pets_species ON public.pets(species);
CREATE INDEX idx_pets_is_memorial ON public.pets(is_memorial);

-- Memory indexes
CREATE INDEX idx_memories_pet_id ON public.memories(pet_id);
CREATE INDEX idx_memories_user_id ON public.memories(user_id);
CREATE INDEX idx_memories_created_at ON public.memories(created_at);
CREATE INDEX idx_memories_memory_type ON public.memories(memory_type);
CREATE INDEX idx_memories_is_public ON public.memories(is_public);

-- Community indexes
CREATE INDEX idx_community_posts_user_id ON public.community_posts(user_id);
CREATE INDEX idx_community_posts_created_at ON public.community_posts(created_at);
CREATE INDEX idx_community_posts_is_public ON public.community_posts(is_public);

-- Marketplace indexes
CREATE INDEX idx_marketplace_products_category ON public.marketplace_products(category);
CREATE INDEX idx_marketplace_products_is_active ON public.marketplace_products(is_active);
CREATE INDEX idx_marketplace_orders_buyer_id ON public.marketplace_orders(buyer_id);
CREATE INDEX idx_marketplace_orders_status ON public.marketplace_orders(status);

-- Analytics indexes
CREATE INDEX idx_analytics_events_user_id ON public.analytics_events(user_id);
CREATE INDEX idx_analytics_events_event_type ON public.analytics_events(event_type);
CREATE INDEX idx_analytics_events_created_at ON public.analytics_events(created_at);

-- =============================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memory_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_prompts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.video_montages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pet_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.community_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memorial_gardens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memorial_flowers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memorial_tributes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketplace_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketplace_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics_events ENABLE ROW LEVEL SECURITY;

-- Users can read their own data
CREATE POLICY "Users can read own data" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own data" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Pet policies
CREATE POLICY "Users can manage own pets" ON public.pets
    FOR ALL USING (auth.uid() = owner_id);

CREATE POLICY "Users can view public pets" ON public.pets
    FOR SELECT USING (true); -- All pets are discoverable

-- Memory policies
CREATE POLICY "Users can manage own memories" ON public.memories
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view public memories" ON public.memories
    FOR SELECT USING (is_public = true);

-- Community policies
CREATE POLICY "Users can manage own posts" ON public.community_posts
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view public posts" ON public.community_posts
    FOR SELECT USING (is_public = true);

-- Marketplace policies
CREATE POLICY "Users can view active products" ON public.marketplace_products
    FOR SELECT USING (is_active = true);

CREATE POLICY "Sellers can manage own products" ON public.marketplace_products
    FOR ALL USING (auth.uid() = seller_id);

-- Order policies
CREATE POLICY "Users can view own orders" ON public.marketplace_orders
    FOR SELECT USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

-- =============================================
-- FUNCTIONS & TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pets_updated_at BEFORE UPDATE ON public.pets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_memories_updated_at BEFORE UPDATE ON public.memories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate revenue metrics
CREATE OR REPLACE FUNCTION calculate_daily_revenue(target_date DATE)
RETURNS void AS $$
BEGIN
    INSERT INTO public.revenue_metrics (
        date,
        subscription_revenue,
        marketplace_revenue,
        total_revenue,
        marketplace_orders
    )
    SELECT 
        target_date,
        COALESCE(sub_revenue, 0),
        COALESCE(marketplace_revenue, 0),
        COALESCE(sub_revenue, 0) + COALESCE(marketplace_revenue, 0),
        COALESCE(order_count, 0)
    FROM (
        SELECT 
            SUM(amount) as sub_revenue
        FROM public.subscription_transactions 
        WHERE DATE(created_at) = target_date AND status = 'completed'
    ) sub
    CROSS JOIN (
        SELECT 
            SUM(commission_amount) as marketplace_revenue,
            COUNT(*) as order_count
        FROM public.marketplace_orders 
        WHERE DATE(created_at) = target_date AND status IN ('paid', 'delivered')
    ) marketplace
    ON CONFLICT (date) DO UPDATE SET
        subscription_revenue = EXCLUDED.subscription_revenue,
        marketplace_revenue = EXCLUDED.marketplace_revenue,
        total_revenue = EXCLUDED.total_revenue,
        marketplace_orders = EXCLUDED.marketplace_orders;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- SAMPLE DATA FOR DEMO
-- =============================================

-- Insert sample revenue data for demo
INSERT INTO public.revenue_metrics (date, subscription_revenue, marketplace_revenue, total_revenue, active_subscribers, marketplace_orders)
VALUES 
    (CURRENT_DATE - INTERVAL '30 days', 1200000, 300000, 1500000, 60000, 1200),
    (CURRENT_DATE - INTERVAL '29 days', 1210000, 310000, 1520000, 60500, 1250),
    (CURRENT_DATE - INTERVAL '28 days', 1220000, 320000, 1540000, 61000, 1300),
    (CURRENT_DATE - INTERVAL '1 day', 1750000, 350000, 2100000, 85000, 1800),
    (CURRENT_DATE, 1750000, 350000, 2100000, 85000, 1850);

-- Success message
SELECT 'PetTime Capsule database schema created successfully! Ready for $2M/month revenue platform.' as status;
