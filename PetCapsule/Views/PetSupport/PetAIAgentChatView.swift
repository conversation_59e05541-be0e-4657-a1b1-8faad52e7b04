//
//  PetAIAgentChatView.swift
//  PetCapsule
//
//  AI Agent Chat Interface with LangGraph-inspired architecture
//  🤖 Advanced conversational AI for pet support
//

import SwiftUI

struct PetAIAgentChatView: View {
    let agent: AIAgent
    @EnvironmentObject private var realDataService: RealDataService
    @Environment(\.dismiss) private var dismiss

    @State private var messages: [ChatMessage] = []
    @State private var currentMessage = ""
    @State private var isTyping = false
    @State private var showPetSelector = false
    @State private var selectedPet: Pet?
    @State private var animateMessages = false

    @StateObject private var chatService = PetAIChatService.shared

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Agent Header
                agentHeaderSection

                // Pet Context Selector
                if !realDataService.pets.isEmpty {
                    petContextSection
                }

                // Chat Messages
                chatMessagesSection

                // Input Section
                chatInputSection
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(Color(hex: agent.gradientColors.first ?? "#000000"))
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Clear") {
                        messages.removeAll()
                    }
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                }
            }
            .onAppear {
                setupInitialChat()
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateMessages = true
                }
            }
        }
    }

    // MARK: - Agent Header Section

    private var agentHeaderSection: some View {
        HStack(spacing: 16) {
            // Agent Avatar
            ZStack {
                Circle()
                    .fill(LinearGradient(
                        gradient: Gradient(colors: agent.gradientColors.map { Color(hex: $0) }),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 50, height: 50)

                Text(agent.iconName)
                    .font(.title2)
            }

            // Agent Info
            VStack(alignment: .leading, spacing: 4) {
                Text(agent.name)
                    .font(.petSubheadline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                HStack {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 8, height: 8)

                    Text("Online • Ready to help")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            // Specialties
            VStack(alignment: .trailing, spacing: 2) {
                ForEach(Array(agent.specialties.prefix(2)), id: \.self) { specialty in
                    Text(specialty)
                        .font(.petCaption2)
                        .fontWeight(.medium)
                        .foregroundColor(Color(hex: agent.gradientColors.first ?? "#000000"))
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(Color(hex: agent.gradientColors.first ?? "#000000").opacity(0.1))
                        )
                }
            }
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .scaleEffect(animateMessages ? 1.0 : 0.9)
        .opacity(animateMessages ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateMessages)
    }

    // MARK: - Pet Context Section

    private var petContextSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Chat about:")
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                Spacer()
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    // All pets option
                    petContextButton(
                        title: "General",
                        isSelected: selectedPet == nil,
                        action: { selectedPet = nil }
                    )

                    // Individual pets
                    ForEach(realDataService.pets) { pet in
                        petContextButton(
                            title: pet.name,
                            isSelected: selectedPet?.id == pet.id,
                            action: { selectedPet = pet }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
        .scaleEffect(animateMessages ? 1.0 : 0.9)
        .opacity(animateMessages ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateMessages)
    }

    private func petContextButton(title: String, isSelected: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Text(title)
                .font(.petCaption2)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : Color(hex: agent.gradientColors.first ?? "#000000"))
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    Capsule()
                        .fill(isSelected ? Color(hex: agent.gradientColors.first ?? "#000000") : Color(hex: agent.gradientColors.first ?? "#000000").opacity(0.1))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Chat Messages Section

    private var chatMessagesSection: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 16) {
                    ForEach(Array(messages.enumerated()), id: \.element.id) { index, message in
                        chatMessageView(message: message)
                            .scaleEffect(animateMessages ? 1.0 : 0.8)
                            .opacity(animateMessages ? 1.0 : 0.0)
                            .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1 + 0.3), value: animateMessages)
                    }

                    if isTyping {
                        typingIndicator
                    }
                }
                .padding()
                .padding(.bottom, 100)
            }
            .onChange(of: messages.count) { _ in
                if let lastMessage = messages.last {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }

    private func chatMessageView(message: ChatMessage) -> some View {
        HStack {
            if message.isFromUser {
                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text(message.content)
                        .font(.petSubheadline)
                        .foregroundColor(.white)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color(hex: agent.gradientColors.first ?? "#000000"))
                        )

                    Text(formatTime(message.timestamp))
                        .font(.petCaption2)
                        .foregroundColor(.secondary)
                }
            } else {
                HStack(alignment: .top, spacing: 12) {
                    // Agent Avatar
                    ZStack {
                        Circle()
                            .fill(LinearGradient(
                                gradient: Gradient(colors: agent.gradientColors.map { Color(hex: $0) }),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .frame(width: 32, height: 32)

                        Text(agent.iconName)
                            .font(.caption)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text(message.content)
                            .font(.petSubheadline)
                            .foregroundColor(.primary)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color(.systemGray6))
                            )

                        Text(formatTime(message.timestamp))
                            .font(.petCaption2)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()
            }
        }
    }

    private var typingIndicator: some View {
        HStack {
            HStack(alignment: .top, spacing: 12) {
                // Agent Avatar
                ZStack {
                    Circle()
                        .fill(LinearGradient(
                            gradient: Gradient(colors: agent.gradientColors.map { Color(hex: $0) }),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .frame(width: 32, height: 32)

                    Text(agent.iconName)
                        .font(.caption)
                }

                HStack(spacing: 4) {
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(Color.secondary)
                            .frame(width: 8, height: 8)
                            .scaleEffect(isTyping ? 1.0 : 0.5)
                            .animation(.easeInOut(duration: 0.6).repeatForever().delay(Double(index) * 0.2), value: isTyping)
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemGray6))
                )
            }

            Spacer()
        }
    }

    // MARK: - Chat Input Section

    private var chatInputSection: some View {
        VStack(spacing: 12) {
            // Quick Actions
            if messages.isEmpty {
                quickActionsSection
            }

            // Input Field
            HStack(spacing: 12) {
                TextField("Ask \(agent.name) anything...", text: $currentMessage, axis: .vertical)
                    .textFieldStyle(PlainTextFieldStyle())
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color(.systemGray6))
                    )
                    .lineLimit(1...4)

                Button(action: sendMessage) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(currentMessage.isEmpty ? .secondary : Color(hex: agent.gradientColors.first ?? "#000000"))
                }
                .disabled(currentMessage.isEmpty || isTyping)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }

    private var quickActionsSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(getQuickActions(), id: \.self) { action in
                    Button(action: {
                        currentMessage = action
                        sendMessage()
                    }) {
                        Text(action)
                            .font(.petCaption2)
                            .fontWeight(.medium)
                            .foregroundColor(Color(hex: agent.gradientColors.first ?? "#000000"))
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(hex: agent.gradientColors.first ?? "#000000").opacity(0.1))
                                    .stroke(Color(hex: agent.gradientColors.first ?? "#000000").opacity(0.3), lineWidth: 1)
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal)
        }
    }

    // MARK: - Helper Methods

    private func setupInitialChat() {
        let welcomeMessage = ChatMessage(
            content: getWelcomeMessage(),
            isFromUser: false
        )
        messages.append(welcomeMessage)
    }

    private func sendMessage() {
        guard !currentMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        let userMessage = ChatMessage(
            content: currentMessage,
            isFromUser: true
        )
        messages.append(userMessage)

        let messageToSend = currentMessage
        currentMessage = ""

        // Simulate AI response
        isTyping = true

        Task {
            let response = await chatService.getAIResponse(
                message: messageToSend,
                agent: agent,
                petContext: selectedPet,
                chatHistory: messages
            )

            await MainActor.run {
                isTyping = false
                let aiMessage = ChatMessage(
                    content: response,
                    isFromUser: false
                )
                messages.append(aiMessage)
            }
        }
    }

    private func getWelcomeMessage() -> String {
        switch agent.name {
        case "Dr. Nutrition":
            return "Hi! I'm Dr. Nutrition 🥗 I'm here to help you create the perfect diet plan for your furry friend. What would you like to know about pet nutrition?"
        case "Health Guardian":
            return "Hello! I'm Health Guardian 🏥 I can help monitor your pet's health and provide insights. Remember, I'm here to support but always consult your vet for serious concerns!"
        case "Style Guru":
            return "Hey there! I'm Style Guru ✂️ Ready to make your pet look fabulous? I can help with grooming tips, styling advice, and hygiene routines!"
        case "Trainer Pro":
            return "Woof! I'm Trainer Pro 🎾 Let's work together to train your pet using positive reinforcement techniques. What behavior would you like to work on?"
        case "Shopping Assistant":
            return "Hi! I'm Shopping Assistant 🛍️ I'll help you find the best products for your pet at great prices. What are you looking for today?"
        case "Wellness Coach":
            return "Namaste! I'm Wellness Coach 🧘‍♀️ I focus on your pet's overall well-being, including mental health and stress reduction. How can I help your pet live their best life?"
        default:
            return "Hello! I'm here to help you with all your pet care needs. What would you like to know?"
        }
    }

    private func getQuickActions() -> [String] {
        switch agent.name {
        case "Dr. Nutrition":
            return ["What should I feed my puppy?", "Is this food safe for cats?", "Create a meal plan", "Weight management tips"]
        case "Health Guardian":
            return ["Check symptoms", "Vaccination schedule", "Emergency signs", "Preventive care tips"]
        case "Style Guru":
            return ["Grooming schedule", "Nail trimming tips", "Coat care advice", "Styling for special occasions"]
        case "Trainer Pro":
            return ["Stop excessive barking", "House training help", "Leash training", "Socialization tips"]
        case "Shopping Assistant":
            return ["Best dog food brands", "Toy recommendations", "Compare prices", "Product reviews"]
        case "Wellness Coach":
            return ["Reduce pet anxiety", "Exercise routines", "Mental stimulation", "Stress management"]
        default:
            return ["General pet care", "Health questions", "Behavior tips", "Product advice"]
        }
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Chat Message Model
// ChatMessage is now defined in Models/ChatMessage.swift

#Preview {
    PetAIAgentChatView(agent: EnhancedAIAgentService.shared.availableAgents[0])
        .environmentObject(RealDataService())
}
