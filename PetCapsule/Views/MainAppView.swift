//
//  MainAppView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct MainAppView: View {
    @EnvironmentObject var supabaseService: SupabaseService
    @EnvironmentObject var mockDataService: MockDataService
    @EnvironmentObject var authService: AuthenticationService
    @State private var isLoading = true
    @State private var showOnboarding = true
    @State private var hasSkippedAuth = false

    var body: some View {
        Group {
            if isLoading {
                SplashView()
            } else if showOnboarding && !authService.isAuthenticated && !hasSkippedAuth {
                OnboardingView(showOnboarding: $showOnboarding, hasSkippedAuth: $hasSkippedAuth)
                    .environmentObject(mockDataService)
                    .environmentObject(supabaseService)
                    .environmentObject(authService)
            } else if authService.isAuthenticated || hasSkippedAuth {
                MainTabView()
                    .environmentObject(mockDataService)
                    .environmentObject(authService)
                    .environmentObject(supabaseService)
            } else {
                AuthenticationView()
                    .environmentObject(authService)
            }
        }
        .onAppear {
            // Simulate loading time for splash screen
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                withAnimation(.easeInOut(duration: 0.5)) {
                    isLoading = false
                }
            }
        }
    }
}

struct SplashView: View {
    @State private var scale: CGFloat = 0.8
    @State private var opacity: Double = 0.5
    @State private var petRotation: Double = 0
    @State private var showPets = false
    @State private var heartBeat = false

    var body: some View {
        ZStack {
            // Enhanced gradient background
            LinearGradient(
                colors: [
                    Color(red: 1.0, green: 0.7, blue: 0.8),
                    Color(red: 0.8, green: 0.6, blue: 1.0),
                    Color(red: 0.7, green: 0.9, blue: 1.0)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            // Floating pet emojis
            if showPets {
                ForEach(0..<6, id: \.self) { index in
                    Text(["🐕", "🐱", "🐰", "🐹", "🐦", "🐠"][index])
                        .font(.title)
                        .offset(
                            x: cos(Double(index) * .pi / 3 + petRotation) * 150,
                            y: sin(Double(index) * .pi / 3 + petRotation) * 150
                        )
                        .opacity(0.6)
                        .animation(
                            .linear(duration: 8).repeatForever(autoreverses: false),
                            value: petRotation
                        )
                }
            }

            VStack(spacing: 24) {
                // Enhanced app icon with heart beat
                ZStack {
                    // Background glow
                    Circle()
                        .fill(Color.white.opacity(0.2))
                        .frame(width: 120, height: 120)
                        .scaleEffect(heartBeat ? 1.2 : 1.0)
                        .opacity(heartBeat ? 0.3 : 0.6)
                        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: heartBeat)

                    // Main heart icon
                    Image(systemName: "heart.circle.fill")
                        .font(.system(size: 80))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.pink, .purple, .blue],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .scaleEffect(scale)
                        .opacity(opacity)
                        .shadow(color: .pink.opacity(0.3), radius: 10, x: 0, y: 5)
                }

                VStack(spacing: 12) {
                    Text("PetTime Capsule")
                        .font(.petLargeTitle)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.white, .white.opacity(0.9)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                        .opacity(opacity)

                    Text("Preserving precious moments forever")
                        .font(.petSubheadline)
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.center)
                        .opacity(opacity)
                }

                // Loading indicator
                HStack(spacing: 8) {
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(Color.white.opacity(0.8))
                            .frame(width: 8, height: 8)
                            .scaleEffect(heartBeat ? 1.2 : 0.8)
                            .animation(
                                .easeInOut(duration: 0.6)
                                    .repeatForever(autoreverses: true)
                                    .delay(Double(index) * 0.2),
                                value: heartBeat
                            )
                    }
                }
                .opacity(opacity)
            }
        }
        .onAppear {
            // Start main animations
            withAnimation(.easeInOut(duration: 1.5)) {
                scale = 1.1
                opacity = 1.0
            }

            // Start heart beat
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                heartBeat = true
            }

            // Show floating pets
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                showPets = true
                withAnimation(.linear(duration: 8).repeatForever(autoreverses: false)) {
                    petRotation = .pi * 2
                }
            }
        }
    }
}

struct MainTabView: View {
    @EnvironmentObject var mockDataService: MockDataService
    @EnvironmentObject var authService: AuthenticationService
    @StateObject private var realDataService = RealDataService()
    @StateObject private var aiSupportService = PetAISupportService.shared
    @State private var selectedTab = 0
    @State private var showAddPet = false

    var body: some View {
        TabView(selection: $selectedTab) {
            // Dashboard - Main hub with all features
            PetDashboardView()
                .tabItem {
                    Image(systemName: selectedTab == 0 ? "house.fill" : "house")
                    Text("Dashboard")
                }
                .tag(0)

            // My Pets - Pet management with AI health
            MyPetsView()
                .tabItem {
                    Image(systemName: selectedTab == 1 ? "pawprint.fill" : "pawprint")
                    Text("My Pets")
                }
                .tag(1)

            // Pet Support - AI Agents for comprehensive pet care
            PetSupportView()
                .tabItem {
                    Image(systemName: selectedTab == 2 ? "brain.head.profile.fill" : "brain.head.profile")
                    Text("Pet Support")
                }
                .tag(2)

            // Memories & Vault - Photo/video management
            MemoryVaultView()
                .tabItem {
                    Image(systemName: selectedTab == 3 ? "photo.fill.on.rectangle.fill" : "photo.on.rectangle")
                    Text("Memories")
                }
                .tag(3)

            // More - Settings, Premium, Pet Health
            MoreView()
                .tabItem {
                    Image(systemName: selectedTab == 4 ? "ellipsis.circle.fill" : "ellipsis.circle")
                    Text("More")
                }
                .tag(4)
        }
        .accentColor(.purple)
        .environmentObject(mockDataService)
        .environmentObject(realDataService)
        .environmentObject(aiSupportService)
        .environmentObject(authService)
        .sheet(isPresented: $showAddPet) {
            AddPetView()
                .environmentObject(realDataService)
                .environmentObject(mockDataService)
                .environmentObject(authService)
        }
        .onAppear {
            setupTabBarAppearance()
        }
    }

    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground

        // Selected item appearance
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor.systemPurple
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.systemPurple,
            .font: UIFont.systemFont(ofSize: 12, weight: .semibold)
        ]

        // Normal item appearance
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.systemGray
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.systemGray,
            .font: UIFont.systemFont(ofSize: 12, weight: .medium)
        ]

        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

// MARK: - Placeholder Views (to be implemented)

struct HomeView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Welcome to PetTime Capsule")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                Text("Your pet's memories, preserved forever")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()
            }
            .navigationTitle("Home")
        }
    }
}

struct MemoriesView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Memories")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                Spacer()
            }
            .navigationTitle("Memories")
        }
    }
}

struct VaultsView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Time Vaults")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                Spacer()
            }
            .navigationTitle("Vaults")
        }
    }
}

struct NetworkView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Pet Network")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()

                Spacer()
            }
            .navigationTitle("Network")
        }
    }
}



#Preview {
    MainAppView()
        .environmentObject(SupabaseService.shared)
}
