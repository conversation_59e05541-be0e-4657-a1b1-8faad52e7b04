//
//  PetDetailView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct PetDetailView: View {
    let pet: Pet
    @EnvironmentObject var mockDataService: MockDataService
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab = 0
    @State private var showEditPet = false
    @State private var animateCards = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Pet Header
                petHeaderSection

                // Tab Selector
                tabSelectorSection

                // Content based on selected tab
                TabView(selection: $selectedTab) {
                    overviewTab
                        .tag(0)

                    healthTab
                        .tag(1)

                    memoriesTab
                        .tag(2)

                    aiInsightsTab
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle(pet.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Close") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("Edit") {
                        showEditPet = true
                    }
                }
            }
            .sheet(isPresented: $showEditPet) {
                EditPetView(pet: pet)
                    .environmentObject(mockDataService)
            }
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    animateCards = true
                }
            }
        }
    }

    // MARK: - Pet Header Section

    private var petHeaderSection: some View {
        VStack(spacing: 16) {
            // Pet Image and Basic Info
            HStack(spacing: 16) {
                // Pet Image
                AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    ZStack {
                        Circle()
                            .fill(Color.purple.opacity(0.2))

                        Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                            .font(.system(size: 40))
                    }
                }
                .frame(width: 100, height: 100)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color.purple, lineWidth: 3)
                )

                // Pet Info
                VStack(alignment: .leading, spacing: 8) {
                    Text(pet.name)
                        .font(.petTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text("\(pet.breed) • \(pet.age) years old")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)

                    // Health Score
                    HStack(spacing: 8) {
                        Circle()
                            .fill(pet.healthScore > 0.8 ? .green : pet.healthScore > 0.6 ? .orange : .red)
                            .frame(width: 12, height: 12)

                        Text("\(Int(pet.healthScore * 100))% Health Score")
                            .font(.petSubheadline)
                            .foregroundColor(.secondary)
                    }

                    // Subscription Badge
                    if pet.subscriptionTier != "free" {
                        Text(pet.subscriptionTier.uppercased())
                            .font(.petCaption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(Color.purple)
                            )
                    }
                }

                Spacer()
            }

            // Quick Stats
            HStack(spacing: 16) {
                quickStatCard(
                    icon: "photo.fill",
                    title: "Memories",
                    value: "\(pet.storedMemoryCount)",
                    color: .blue
                )

                quickStatCard(
                    icon: "heart.circle.fill",
                    title: "Friends",
                    value: "\(pet.friendsCount)",
                    color: .pink
                )

                quickStatCard(
                    icon: "trophy.fill",
                    title: "Badges",
                    value: "\(pet.achievementBadges.count)",
                    color: .yellow
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
        .padding()
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
    }

    private func quickStatCard(icon: String, title: String, value: String, color: Color) -> some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.petCaption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }

    // MARK: - Tab Selector Section

    private var tabSelectorSection: some View {
        HStack(spacing: 0) {
            tabButton(title: "Overview", icon: "info.circle.fill", index: 0)
            tabButton(title: "Health", icon: "heart.fill", index: 1)
            tabButton(title: "Memories", icon: "photo.fill", index: 2)
            tabButton(title: "AI Insights", icon: "brain.head.profile", index: 3)
        }
        .padding(.horizontal)
        .background(Color(.systemBackground))
    }

    private func tabButton(title: String, icon: String, index: Int) -> some View {
        Button(action: { selectedTab = index }) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)

                Text(title)
                    .font(.petCaption)
                    .fontWeight(.medium)
                    .foregroundColor(selectedTab == index ? .purple : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedTab == index ? Color.purple.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Overview Tab

    private var overviewTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Bio Section
                if !pet.bio.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("About \(pet.name)")
                            .font(.petTitle3)
                            .fontWeight(.bold)

                        Text(pet.bio)
                            .font(.petBody)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemGray6))
                    )
                }

                // Basic Info
                basicInfoSection

                // Personality Traits
                personalityTraitsSection

                // Achievement Badges
                achievementBadgesSection
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    private var basicInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Basic Information")
                .font(.petTitle3)
                .fontWeight(.bold)

            VStack(spacing: 12) {
                infoRow(label: "Species", value: pet.species.capitalized)
                infoRow(label: "Breed", value: pet.breed)
                infoRow(label: "Age", value: "\(pet.age) years old")
                infoRow(label: "Gender", value: pet.gender?.capitalized ?? "Unknown")
                if let weight = pet.weight {
                    infoRow(label: "Weight", value: String(format: "%.1f kg", weight))
                }
                if let microchip = pet.microchipId {
                    infoRow(label: "Microchip", value: microchip)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    private func infoRow(label: String, value: String) -> some View {
        HStack {
            Text(label)
                .font(.petSubheadline)
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.petSubheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
    }

    private var personalityTraitsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Personality Traits")
                .font(.petTitle3)
                .fontWeight(.bold)

            if pet.personalityTraits.isEmpty {
                Text("No personality traits recorded yet")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    ForEach(pet.personalityTraits, id: \.self) { trait in
                        Text(trait)
                            .font(.petSubheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.purple)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.purple.opacity(0.1))
                            )
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    private var achievementBadgesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Achievement Badges")
                .font(.petTitle3)
                .fontWeight(.bold)

            if pet.achievementBadges.isEmpty {
                Text("No badges earned yet")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                    ForEach(pet.achievementBadges, id: \.self) { badge in
                        VStack(spacing: 6) {
                            Image(systemName: "trophy.fill")
                                .font(.title2)
                                .foregroundColor(.yellow)

                            Text(badge)
                                .font(.petCaption)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.center)
                                .lineLimit(2)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.yellow.opacity(0.1))
                        )
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // MARK: - Health Tab

    private var healthTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                Text("Health information for \(pet.name)")
                    .font(.petTitle3)
                    .foregroundColor(.secondary)
                    .padding()

                // Placeholder for health content
                Text("Health details will be implemented here")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    // MARK: - Memories Tab

    private var memoriesTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                Text("Memories of \(pet.name)")
                    .font(.petTitle3)
                    .foregroundColor(.secondary)
                    .padding()

                // Placeholder for memories content
                Text("Pet memories will be displayed here")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    // MARK: - AI Insights Tab

    private var aiInsightsTab: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                Text("AI insights for \(pet.name)")
                    .font(.petTitle3)
                    .foregroundColor(.secondary)
                    .padding()

                // Placeholder for AI insights content
                Text("AI recommendations and insights will be shown here")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            }
            .padding()
            .padding(.bottom, 100)
        }
    }
}

// MARK: - Edit Pet View Placeholder

struct EditPetView: View {
    let pet: Pet
    @EnvironmentObject var mockDataService: MockDataService
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Edit \(pet.name)")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .padding()

                Text("Pet editing functionality will be implemented here")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()

                Spacer()
            }
            .navigationTitle("Edit Pet")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        dismiss()
                    }
                }
            }
        }
    }
}
