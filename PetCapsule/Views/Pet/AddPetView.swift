//
//  AddPetView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import PhotosUI

struct AddPetView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var realDataService: RealDataService

    // Basic Info
    @State private var name = ""
    @State private var species = "dog"
    @State private var breed = ""
    @State private var age = 1
    @State private var dateOfBirth = Date()
    @State private var gender = "male"
    @State private var bio = ""

    // Health Info
    @State private var weight = ""
    @State private var microchipId = ""
    @State private var vetName = ""
    @State private var vetContact = ""
    @State private var allergies: [String] = []
    @State private var medications: [String] = []
    @State private var newAllergy = ""
    @State private var newMedication = ""

    // Nutrition Info
    @State private var currentFood = ""
    @State private var foodBrand = ""
    @State private var activityLevel = "moderate"

    // Photo
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var profileImage: UIImage?

    // UI State
    @State private var currentStep = 0
    @State private var showingImagePicker = false
    @State private var isLoading = false

    let species_options = ["dog", "cat", "bird", "rabbit", "hamster", "fish", "reptile", "other"]
    let gender_options = ["male", "female", "unknown"]
    let activity_levels = ["low", "moderate", "high", "very_high"]

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Progress Bar
                progressBar

                // Step Content
                Group {
                    switch currentStep {
                    case 0: basicInfoStep
                    case 1: healthInfoStep
                    case 2: nutritionStep
                    case 3: photoStep
                    case 4: reviewStep
                    default: basicInfoStep
                    }
                }
                .animation(.easeInOut(duration: 0.3), value: currentStep)

                // Navigation Buttons
                navigationButtons
            }
            .navigationTitle("Add New Pet")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }

    // MARK: - Progress Bar

    private var progressBar: some View {
        VStack(spacing: 8) {
            HStack {
                ForEach(0..<5) { step in
                    Circle()
                        .fill(step <= currentStep ? Color.purple : Color(.systemGray4))
                        .frame(width: 12, height: 12)
                        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: currentStep)

                    if step < 4 {
                        Rectangle()
                            .fill(step < currentStep ? Color.purple : Color(.systemGray4))
                            .frame(height: 2)
                            .animation(.spring(response: 0.5, dampingFraction: 0.8), value: currentStep)
                    }
                }
            }

            Text(stepTitle)
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(.purple)
        }
        .padding()
    }

    private var stepTitle: String {
        switch currentStep {
        case 0: return "Basic Information"
        case 1: return "Health Details"
        case 2: return "Nutrition & Activity"
        case 3: return "Add Photo"
        case 4: return "Review & Save"
        default: return ""
        }
    }

    // MARK: - Step 1: Basic Info

    private var basicInfoStep: some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Tell us about your pet")
                        .font(.petTitle2)
                        .fontWeight(.bold)

                    // Name
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Pet Name *")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("Enter your pet's name", text: $name)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    // Species
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Species *")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        Picker("Species", selection: $species) {
                            ForEach(species_options, id: \.self) { option in
                                HStack {
                                    Text(option == "dog" ? "🐕" : option == "cat" ? "🐱" : "🐾")
                                    Text(option.capitalized)
                                }
                                .tag(option)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(.systemGray6))
                        )
                    }

                    // Breed
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Breed")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("e.g., Golden Retriever, Maine Coon", text: $breed)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    // Age and Gender
                    HStack(spacing: 16) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Age *")
                                .font(.petSubheadline)
                                .fontWeight(.semibold)

                            Stepper("\(age) years old", value: $age, in: 0...30)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color(.systemGray6))
                                )
                        }

                        VStack(alignment: .leading, spacing: 8) {
                            Text("Gender")
                                .font(.petSubheadline)
                                .fontWeight(.semibold)

                            Picker("Gender", selection: $gender) {
                                ForEach(gender_options, id: \.self) { option in
                                    Text(option.capitalized).tag(option)
                                }
                            }
                            .pickerStyle(SegmentedPickerStyle())
                        }
                    }

                    // Bio
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Bio")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("Tell us about your pet's personality...", text: $bio, axis: .vertical)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .lineLimit(3...6)
                    }
                }
            }
            .padding()
        }
    }

    // MARK: - Step 2: Health Info

    private var healthInfoStep: some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Health Information")
                        .font(.petTitle2)
                        .fontWeight(.bold)

                    // Weight
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Weight (kg)")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("Enter weight", text: $weight)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .keyboardType(.decimalPad)
                    }

                    // Microchip ID
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Microchip ID")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("15-digit microchip number", text: $microchipId)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    // Vet Information
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Veterinarian")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("Vet name", text: $vetName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())

                        TextField("Vet contact", text: $vetContact)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    // Allergies
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Allergies")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        HStack {
                            TextField("Add allergy", text: $newAllergy)
                                .textFieldStyle(RoundedBorderTextFieldStyle())

                            Button("Add") {
                                if !newAllergy.isEmpty {
                                    allergies.append(newAllergy)
                                    newAllergy = ""
                                }
                            }
                            .disabled(newAllergy.isEmpty)
                        }

                        if !allergies.isEmpty {
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                                ForEach(allergies, id: \.self) { allergy in
                                    HStack {
                                        Text(allergy)
                                            .font(.petCaption)

                                        Spacer()

                                        Button(action: {
                                            allergies.removeAll { $0 == allergy }
                                        }) {
                                            Image(systemName: "xmark.circle.fill")
                                                .foregroundColor(.red)
                                        }
                                    }
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 6)
                                            .fill(Color(.systemGray6))
                                    )
                                }
                            }
                        }
                    }

                    // Medications
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Medications")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        HStack {
                            TextField("Add medication", text: $newMedication)
                                .textFieldStyle(RoundedBorderTextFieldStyle())

                            Button("Add") {
                                if !newMedication.isEmpty {
                                    medications.append(newMedication)
                                    newMedication = ""
                                }
                            }
                            .disabled(newMedication.isEmpty)
                        }

                        if !medications.isEmpty {
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 1), spacing: 8) {
                                ForEach(medications, id: \.self) { medication in
                                    HStack {
                                        Text(medication)
                                            .font(.petCaption)

                                        Spacer()

                                        Button(action: {
                                            medications.removeAll { $0 == medication }
                                        }) {
                                            Image(systemName: "xmark.circle.fill")
                                                .foregroundColor(.red)
                                        }
                                    }
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 6)
                                            .fill(Color(.systemGray6))
                                    )
                                }
                            }
                        }
                    }
                }
            }
            .padding()
        }
    }

    // MARK: - Step 3: Nutrition

    private var nutritionStep: some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Nutrition & Activity")
                        .font(.petTitle2)
                        .fontWeight(.bold)

                    // Current Food
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Food")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        TextField("Food name", text: $currentFood)
                            .textFieldStyle(RoundedBorderTextFieldStyle())

                        TextField("Brand", text: $foodBrand)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    // Activity Level
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Activity Level")
                            .font(.petSubheadline)
                            .fontWeight(.semibold)

                        Picker("Activity Level", selection: $activityLevel) {
                            Text("Low - Couch Potato").tag("low")
                            Text("Moderate - Regular walks").tag("moderate")
                            Text("High - Very active").tag("high")
                            Text("Very High - Athletic").tag("very_high")
                        }
                        .pickerStyle(MenuPickerStyle())
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(.systemGray6))
                        )
                    }
                }
            }
            .padding()
        }
    }

    // MARK: - Step 4: Photo

    private var photoStep: some View {
        VStack(spacing: 24) {
            Text("Add a Photo")
                .font(.petTitle2)
                .fontWeight(.bold)

            if let profileImage = profileImage {
                Image(uiImage: profileImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 200, height: 200)
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                    .shadow(radius: 10)
            } else {
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color(.systemGray6))
                    .frame(width: 200, height: 200)
                    .overlay(
                        VStack {
                            Image(systemName: "camera.fill")
                                .font(.system(size: 40))
                                .foregroundColor(.purple)

                            Text("Add Photo")
                                .font(.petSubheadline)
                                .foregroundColor(.purple)
                        }
                    )
            }

            PhotosPicker(selection: $selectedPhoto, matching: .images) {
                Text(profileImage == nil ? "Choose Photo" : "Change Photo")
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
            }
            .onChange(of: selectedPhoto) { _, newValue in
                Task {
                    if let data = try? await newValue?.loadTransferable(type: Data.self),
                       let image = UIImage(data: data) {
                        profileImage = image
                    }
                }
            }

            Text("Adding a photo helps with AI analysis and makes your pet's profile more personal")
                .font(.petCaption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            Spacer()
        }
        .padding()
    }

    // MARK: - Step 5: Review

    private var reviewStep: some View {
        ScrollView {
            VStack(spacing: 24) {
                Text("Review Pet Information")
                    .font(.petTitle2)
                    .fontWeight(.bold)

                VStack(spacing: 16) {
                    if let profileImage = profileImage {
                        Image(uiImage: profileImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 100, height: 100)
                            .clipShape(Circle())
                    }

                    Text(name)
                        .font(.petTitle)
                        .fontWeight(.bold)

                    Text("\(breed) • \(age) years old • \(gender.capitalized)")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                }

                VStack(alignment: .leading, spacing: 16) {
                    reviewSection(title: "Basic Info", items: [
                        ("Species", species.capitalized),
                        ("Bio", bio.isEmpty ? "Not provided" : bio)
                    ])

                    reviewSection(title: "Health", items: [
                        ("Weight", weight.isEmpty ? "Not provided" : "\(weight) kg"),
                        ("Microchip", microchipId.isEmpty ? "Not provided" : microchipId),
                        ("Veterinarian", vetName.isEmpty ? "Not provided" : vetName),
                        ("Allergies", allergies.isEmpty ? "None" : allergies.joined(separator: ", ")),
                        ("Medications", medications.isEmpty ? "None" : medications.joined(separator: ", "))
                    ])

                    reviewSection(title: "Nutrition", items: [
                        ("Current Food", currentFood.isEmpty ? "Not provided" : currentFood),
                        ("Brand", foodBrand.isEmpty ? "Not provided" : foodBrand),
                        ("Activity Level", activityLevel.capitalized)
                    ])
                }
            }
            .padding()
        }
    }

    private func reviewSection(title: String, items: [(String, String)]) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.petSubheadline)
                .fontWeight(.bold)
                .foregroundColor(.purple)

            VStack(alignment: .leading, spacing: 8) {
                ForEach(items, id: \.0) { item in
                    HStack {
                        Text(item.0)
                            .font(.petCaption)
                            .foregroundColor(.secondary)

                        Spacer()

                        Text(item.1)
                            .font(.petCaption)
                            .foregroundColor(.primary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
    }

    // MARK: - Navigation Buttons

    private var navigationButtons: some View {
        HStack {
            if currentStep > 0 {
                Button("Previous") {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        currentStep -= 1
                    }
                }
                .font(.petSubheadline)
                .foregroundColor(.purple)
            }

            Spacer()

            Button(currentStep == 4 ? "Save Pet" : "Next") {
                if currentStep == 4 {
                    savePet()
                } else {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        currentStep += 1
                    }
                }
            }
            .font(.petSubheadline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isFormValid ? Color.purple : Color.gray)
            )
            .disabled(!isFormValid || isLoading)
        }
        .padding()
    }

    private var isFormValid: Bool {
        switch currentStep {
        case 0: return !name.isEmpty && !species.isEmpty
        case 1, 2, 3: return true
        case 4: return !name.isEmpty && !species.isEmpty
        default: return false
        }
    }

    // MARK: - Save Pet

    private func savePet() {
        isLoading = true

        // Create new pet with comprehensive data
        let newPet = Pet(
            name: name,
            species: species,
            breed: breed.isEmpty ? "Mixed" : breed,
            age: age,
            dateOfBirth: Calendar.current.date(byAdding: .year, value: -age, to: Date()),
            profileImageURL: nil, // TODO: Upload image to storage
            bio: bio,
            ownerID: realDataService.getCurrentUserId().uuidString
        )

        // Set additional properties after initialization
        newPet.weight = Double(weight) ?? 0.0
        newPet.activityLevel = activityLevel
        newPet.personalityTraits = generatePersonalityTraits()
        newPet.medications = medications

        // Save to real database
        Task {
            let success = await realDataService.createPet(newPet, userId: realDataService.getCurrentUserId())

            await MainActor.run {
                isLoading = false
                if success {
                    dismiss()
                } else {
                    // Handle error - could show alert
                    print("Failed to save pet")
                }
            }
        }
    }

    private func calculateDailyCalories() -> Int {
        guard let weightValue = Double(weight) else { return 0 }

        let baseCalories = species == "dog" ? (weightValue * 30 + 70) : (weightValue * 70)
        let activityMultiplier: Double = {
            switch activityLevel {
            case "low": return 1.0
            case "moderate": return 1.2
            case "high": return 1.4
            case "very_high": return 1.6
            default: return 1.2
            }
        }()

        return Int(baseCalories * activityMultiplier)
    }

    private func generatePersonalityTraits() -> [String] {
        var traits: [String] = []

        switch activityLevel {
        case "low": traits.append("Calm")
        case "moderate": traits.append("Balanced")
        case "high": traits.append("Energetic")
        case "very_high": traits.append("Very Active")
        default: break
        }

        if species == "dog" {
            traits.append(contentsOf: ["Loyal", "Friendly"])
        } else if species == "cat" {
            traits.append(contentsOf: ["Independent", "Curious"])
        }

        return traits
    }
}
