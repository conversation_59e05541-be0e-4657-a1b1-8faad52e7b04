//
//  MyPetsView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct MyPetsView: View {
    @EnvironmentObject var mockDataService: MockDataService
    @State private var showAddPet = false
    @State private var selectedPet: Pet?
    @State private var searchText = ""
    @State private var selectedFilter: PetFilter = .all
    @State private var animateCards = false

    enum PetFilter: String, CaseIterable {
        case all = "All"
        case dogs = "Dogs"
        case cats = "Cats"
        case needsAttention = "Needs Attention"
        case premium = "Premium"
    }

    var filteredPets: [Pet] {
        let pets = mockDataService.mockPets

        var filtered = pets

        // Apply filter
        switch selectedFilter {
        case .all:
            break
        case .dogs:
            filtered = pets.filter { $0.species == "dog" }
        case .cats:
            filtered = pets.filter { $0.species == "cat" }
        case .needsAttention:
            filtered = pets.filter { !$0.healthAlerts.isEmpty }
        case .premium:
            filtered = pets.filter { $0.subscriptionTier != "free" }
        }

        // Apply search
        if !searchText.isEmpty {
            filtered = filtered.filter { pet in
                pet.name.localizedCaseInsensitiveContains(searchText) ||
                pet.breed.localizedCaseInsensitiveContains(searchText)
            }
        }

        return filtered
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search and Filter Section
                searchAndFilterSection

                // Pets List
                if filteredPets.isEmpty {
                    emptyStateView
                } else {
                    petsListView
                }
            }
            .navigationTitle("My Pets")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showAddPet = true }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.purple)
                    }
                }
            }
            .sheet(isPresented: $showAddPet) {
                AddPetView()
                    .environmentObject(mockDataService)
            }
            .sheet(item: $selectedPet) { pet in
                PetDetailView(pet: pet)
                    .environmentObject(mockDataService)
            }
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    animateCards = true
                }
            }
        }
    }

    // MARK: - Search and Filter Section

    private var searchAndFilterSection: some View {
        VStack(spacing: 16) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("Search pets...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())

                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )

            // Filter Chips
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(PetFilter.allCases, id: \.self) { filter in
                        filterChip(filter: filter)
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }

    private func filterChip(filter: PetFilter) -> some View {
        Button(action: { selectedFilter = filter }) {
            HStack(spacing: 6) {
                Text(filter.rawValue)
                    .font(.petSubheadline)
                    .fontWeight(.medium)

                if filter == .needsAttention {
                    let count = mockDataService.getPetsNeedingAttention().count
                    if count > 0 {
                        Text("\(count)")
                            .font(.petCaption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Circle().fill(Color.red))
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(selectedFilter == filter ? Color.purple : Color(.systemGray6))
            )
            .foregroundColor(selectedFilter == filter ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Pets List View

    private var petsListView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(Array(filteredPets.enumerated()), id: \.element.id) { index, pet in
                    petCard(pet: pet)
                        .scaleEffect(animateCards ? 1.0 : 0.9)
                        .opacity(animateCards ? 1.0 : 0.0)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateCards)
                        .onTapGesture {
                            selectedPet = pet
                        }
                }
            }
            .padding()
            .padding(.bottom, 100)
        }
    }

    private func petCard(pet: Pet) -> some View {
        HStack(spacing: 16) {
            // Pet Image
            AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                ZStack {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.purple.opacity(0.2))

                    Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                        .font(.system(size: 30))
                }
            }
            .frame(width: 80, height: 80)
            .clipShape(RoundedRectangle(cornerRadius: 16))

            // Pet Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(pet.name)
                        .font(.petTitle3)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Spacer()

                    // Subscription badge
                    if pet.subscriptionTier != "free" {
                        Text(pet.subscriptionTier.uppercased())
                            .font(.petCaption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(Color.purple)
                            )
                    }
                }

                HStack {
                    Text(pet.breed)
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)

                    Text("•")
                        .foregroundColor(.secondary)

                    Text("\(pet.age) years old")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                }

                // Health and Stats Row
                HStack(spacing: 16) {
                    // Health Score
                    HStack(spacing: 4) {
                        Circle()
                            .fill(pet.healthScore > 0.8 ? .green : pet.healthScore > 0.6 ? .orange : .red)
                            .frame(width: 8, height: 8)

                        Text("\(Int(pet.healthScore * 100))% Health")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }

                    // Memory Count
                    HStack(spacing: 4) {
                        Image(systemName: "photo.fill")
                            .font(.caption)
                            .foregroundColor(.blue)

                        Text("\(pet.storedMemoryCount) memories")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    // Alert indicator
                    if !pet.healthAlerts.isEmpty {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }

                // AI Recommendations Preview
                if !pet.aiRecommendations.isEmpty {
                    HStack {
                        Image(systemName: "brain.head.profile")
                            .font(.caption)
                            .foregroundColor(.purple)

                        Text("AI: \(pet.aiRecommendations.first!)")
                            .font(.petCaption)
                            .foregroundColor(.purple)
                            .lineLimit(1)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color.purple.opacity(0.1))
                    )
                }
            }

            // Chevron
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
    }

    // MARK: - Empty State View

    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: searchText.isEmpty ? "pawprint.circle.fill" : "magnifyingglass")
                .font(.system(size: 80))
                .foregroundColor(.purple.opacity(0.6))

            VStack(spacing: 8) {
                Text(searchText.isEmpty ? "No pets found" : "No results found")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(searchText.isEmpty ?
                     "Add your first pet to get started with AI health insights" :
                     "Try adjusting your search or filter")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            if searchText.isEmpty {
                Button(action: { showAddPet = true }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                        Text("Add Your First Pet")
                    }
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
                }
            }

            Spacer()
        }
        .padding()
    }
}
