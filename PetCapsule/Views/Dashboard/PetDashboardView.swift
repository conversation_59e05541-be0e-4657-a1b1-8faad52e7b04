//
//  PetDashboardView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct PetDashboardView: View {
    @StateObject private var realDataService = RealDataService()
    @EnvironmentObject var aiSupportService: PetAISupportService
    @State private var selectedPet: Pet?
    @State private var showAddPet = false
    @State private var animateCards = false
    @State private var showHealthAlert = false
    @State private var currentHealthAlert: String = ""

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Welcome Header with Animation
                    welcomeHeader

                    // Quick Stats Cards
                    quickStatsSection

                    // My Pets Section
                    myPetsSection

                    // AI Health Insights
                    aiHealthSection

                    // Recent Memories
                    recentMemoriesSection

                    // Premium Features Showcase
                    premiumFeaturesSection

                    // Quick Actions
                    quickActionsSection
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showAddPet = true }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.purple)
                    }
                }
            }
            .sheet(isPresented: $showAddPet) {
                AddPetView()
                    .environmentObject(realDataService)
            }
            .alert("Health Alert", isPresented: $showHealthAlert) {
                Button("View Details") { }
                Button("Dismiss", role: .cancel) { }
            } message: {
                Text(currentHealthAlert)
            }
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    animateCards = true
                }
                checkHealthAlerts()
            }
        }
    }

    // MARK: - Welcome Header

    private var welcomeHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Welcome back! 🐾")
                        .font(.petTitle2)
                        .foregroundColor(.primary)

                    Text("Your pets are waiting for you")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Weather-like pet mood indicator
                VStack {
                    Image(systemName: "sun.max.fill")
                        .font(.title)
                        .foregroundColor(.orange)
                        .rotationEffect(.degrees(animateCards ? 360 : 0))
                        .animation(.linear(duration: 20).repeatForever(autoreverses: false), value: animateCards)

                    Text("Happy")
                        .font(.petCaption)
                        .foregroundColor(.orange)
                }
            }

            // Today's highlights
            HStack(spacing: 16) {
                highlightCard(
                    icon: "heart.fill",
                    title: "Health Score",
                    value: "92%",
                    color: .green
                )

                highlightCard(
                    icon: "photo.fill",
                    title: "New Memories",
                    value: "5",
                    color: .blue
                )

                highlightCard(
                    icon: "brain.head.profile",
                    title: "AI Insights",
                    value: "3",
                    color: .purple
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        )
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
    }

    private func highlightCard(icon: String, title: String, value: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.petTitle3)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            Text(title)
                .font(.petCaption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }

    // MARK: - Quick Stats Section

    private var quickStatsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Quick Stats")
                    .font(.petTitle2)
                    .fontWeight(.bold)

                Spacer()

                Button("View All") {
                    // Navigate to detailed stats
                }
                .font(.petSubheadline)
                .foregroundColor(.purple)
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                statCard(
                    title: "Total Pets",
                    value: "\(realDataService.pets.count)",
                    icon: "pawprint.fill",
                    color: .orange,
                    trend: "+1 this month"
                )

                statCard(
                    title: "Memories Saved",
                    value: "\(realDataService.memories.count)",
                    icon: "photo.stack.fill",
                    color: .blue,
                    trend: "+12 this week"
                )

                statCard(
                    title: "Health Alerts",
                    value: "\(realDataService.pets.filter { !$0.healthAlerts.isEmpty }.count)",
                    icon: "heart.text.square.fill",
                    color: .red,
                    trend: "2 urgent"
                )

                statCard(
                    title: "AI Insights",
                    value: "15",
                    icon: "brain.head.profile.fill",
                    color: .purple,
                    trend: "3 new today"
                )
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateCards)
    }

    private func statCard(title: String, value: String, icon: String, color: Color, trend: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Spacer()

                Text(trend)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.petTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text(title)
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // MARK: - My Pets Section

    private var myPetsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("My Pets")
                    .font(.petTitle2)
                    .fontWeight(.bold)

                Spacer()

                Button("Manage All") {
                    // Navigate to pet management
                }
                .font(.petSubheadline)
                .foregroundColor(.purple)
            }

            if realDataService.pets.isEmpty {
                emptyPetsView
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        ForEach(Array(realDataService.pets.enumerated()), id: \.element.id) { index, pet in
                            petCard(pet: pet)
                                .scaleEffect(animateCards ? 1.0 : 0.8)
                                .opacity(animateCards ? 1.0 : 0.0)
                                .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1 + 0.3), value: animateCards)
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
    }

    private var emptyPetsView: some View {
        VStack(spacing: 16) {
            Image(systemName: "pawprint.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.purple.opacity(0.6))

            Text("No pets added yet")
                .font(.petTitle3)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text("Add your first pet to get started with AI health insights and memory preservation")
                .font(.petSubheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button(action: { showAddPet = true }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Add Your First Pet")
                }
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.purple)
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }

    private func petCard(pet: Pet) -> some View {
        VStack(spacing: 12) {
            // Pet image or placeholder
            AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.purple.opacity(0.2))

                    Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                        .font(.system(size: 40))
                }
            }
            .frame(width: 80, height: 80)
            .clipShape(RoundedRectangle(cornerRadius: 12))

            VStack(spacing: 4) {
                Text(pet.name)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(pet.breed)
                    .font(.petCaption)
                    .foregroundColor(.secondary)

                // Health indicator
                HStack(spacing: 4) {
                    Circle()
                        .fill(pet.healthScore > 0.8 ? .green : pet.healthScore > 0.6 ? .orange : .red)
                        .frame(width: 8, height: 8)

                    Text("\(Int(pet.healthScore * 100))% Health")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .onTapGesture {
            selectedPet = pet
        }
    }

    // MARK: - AI Health Section

    private var aiHealthSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("AI Health Insights")
                    .font(.petTitle2)
                    .fontWeight(.bold)

                Spacer()

                Button("View All") {
                    // Navigate to AI health center
                }
                .font(.petSubheadline)
                .foregroundColor(.purple)
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    aiInsightCard(
                        icon: "brain.head.profile.fill",
                        title: "Nutrition Analysis",
                        description: "Max needs 15% more protein",
                        priority: .medium,
                        color: .orange
                    )

                    aiInsightCard(
                        icon: "heart.text.square.fill",
                        title: "Exercise Recommendation",
                        description: "Luna needs 20 min more activity",
                        priority: .low,
                        color: .blue
                    )

                    aiInsightCard(
                        icon: "stethoscope",
                        title: "Health Prediction",
                        description: "Charlie: Joint care recommended",
                        priority: .high,
                        color: .red
                    )
                }
                .padding(.horizontal)
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateCards)
    }

    private func aiInsightCard(icon: String, title: String, description: String, priority: Priority, color: Color) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Spacer()

                Text(priority.rawValue.uppercased())
                    .font(.petCaption)
                    .fontWeight(.bold)
                    .foregroundColor(Color(priority.color))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color(priority.color).opacity(0.2))
                    )
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(description)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            Button("View Details") {
                // Navigate to detailed insight
            }
            .font(.petCaption)
            .fontWeight(.semibold)
            .foregroundColor(color)
        }
        .padding()
        .frame(width: 200)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // MARK: - Recent Memories Section

    private var recentMemoriesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Memories")
                    .font(.petTitle2)
                    .fontWeight(.bold)

                Spacer()

                Button("View All") {
                    // Navigate to memories
                }
                .font(.petSubheadline)
                .foregroundColor(.purple)
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                ForEach(0..<6, id: \.self) { index in
                    memoryThumbnail(index: index)
                        .scaleEffect(animateCards ? 1.0 : 0.8)
                        .opacity(animateCards ? 1.0 : 0.0)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.05 + 0.5), value: animateCards)
                }
            }
        }
    }

    private func memoryThumbnail(index: Int) -> some View {
        ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.purple.opacity(0.2))
                .aspectRatio(1, contentMode: .fit)

            VStack {
                Image(systemName: index % 2 == 0 ? "photo.fill" : "video.fill")
                    .font(.title2)
                    .foregroundColor(.purple)

                Text("Memory \(index + 1)")
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }
        }
        .onTapGesture {
            // Navigate to memory detail
        }
    }

    // MARK: - Premium Features Section

    private var premiumFeaturesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Premium Features")
                    .font(.petTitle2)
                    .fontWeight(.bold)

                Spacer()

                Button("Upgrade") {
                    // Navigate to subscription
                }
                .font(.petSubheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.purple)
                )
            }

            HStack(spacing: 16) {
                premiumFeatureCard(
                    icon: "brain.head.profile.fill",
                    title: "AI Health Analysis",
                    description: "Advanced health insights",
                    isLocked: realDataService.pets.first?.subscriptionTier == "free"
                )

                premiumFeatureCard(
                    icon: "video.fill",
                    title: "Video Montages",
                    description: "Professional pet videos",
                    isLocked: realDataService.pets.first?.subscriptionTier == "free"
                )
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.6), value: animateCards)
    }

    private func premiumFeatureCard(icon: String, title: String, description: String, isLocked: Bool) -> some View {
        VStack(spacing: 12) {
            ZStack {
                Image(systemName: icon)
                    .font(.title)
                    .foregroundColor(isLocked ? .gray : .purple)

                if isLocked {
                    Image(systemName: "lock.fill")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(4)
                        .background(Circle().fill(Color.gray))
                        .offset(x: 15, y: -15)
                }
            }

            VStack(spacing: 4) {
                Text(title)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(description)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(isLocked ? Color(.systemGray6) : Color.purple.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(isLocked ? Color.gray.opacity(0.3) : Color.purple.opacity(0.3), lineWidth: 1)
                )
        )
    }

    // MARK: - Quick Actions Section

    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.petTitle2)
                .fontWeight(.bold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                quickActionButton(
                    icon: "plus.circle.fill",
                    title: "Add Memory",
                    color: .blue
                ) {
                    // Navigate to add memory
                }

                quickActionButton(
                    icon: "stethoscope",
                    title: "Health Check",
                    color: .green
                ) {
                    // Navigate to health check
                }

                quickActionButton(
                    icon: "calendar.badge.plus",
                    title: "Schedule Vet",
                    color: .orange
                ) {
                    // Navigate to vet scheduling
                }

                quickActionButton(
                    icon: "heart.circle.fill",
                    title: "Memorial Garden",
                    color: .pink
                ) {
                    // Navigate to memorial garden
                }
            }
        }
        .scaleEffect(animateCards ? 1.0 : 0.9)
        .opacity(animateCards ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.7), value: animateCards)
    }

    private func quickActionButton(icon: String, title: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Text(title)
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 6, x: 0, y: 3)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Helper Methods

    private func checkHealthAlerts() {
        let petsWithAlerts = realDataService.pets.filter { !$0.healthAlerts.isEmpty }
        if let firstPet = petsWithAlerts.first, !firstPet.healthAlerts.isEmpty {
            currentHealthAlert = "\(firstPet.name): \(firstPet.healthAlerts.first!)"
            showHealthAlert = true
        }
    }
}
