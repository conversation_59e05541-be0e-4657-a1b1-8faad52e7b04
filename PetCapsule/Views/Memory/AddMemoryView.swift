//
//  AddMemoryView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import PhotosUI

struct AddMemoryView: View {
    @EnvironmentObject var mockDataService: MockDataService
    @Environment(\.dismiss) private var dismiss

    @State private var selectedPet: Pet?
    @State private var memoryTitle = ""
    @State private var memoryDescription = ""
    @State private var memoryType: MemoryType = .photo
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var memoryImage: UIImage?
    @State private var isPublic = false
    @State private var isFavorite = false

    enum MemoryType: String, CaseIterable {
        case photo = "Photo"
        case video = "Video"
        case milestone = "Milestone"
        case text = "Text Note"
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Pet Selection
                    petSelectionSection

                    // Memory Type
                    memoryTypeSection

                    // Photo/Video Selection
                    if memoryType == .photo || memoryType == .video {
                        mediaSelectionSection
                    }

                    // Memory Details
                    memoryDetailsSection

                    // Options
                    memoryOptionsSection
                }
                .padding()
            }
            .navigationTitle("Add Memory")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveMemory()
                    }
                    .disabled(!isFormValid)
                }
            }
        }
    }

    private var petSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Select Pet")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            if mockDataService.mockPets.isEmpty {
                Text("No pets available. Add a pet first.")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(mockDataService.mockPets, id: \.id) { pet in
                            petSelectionCard(pet: pet)
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
    }

    private func petSelectionCard(pet: Pet) -> some View {
        VStack(spacing: 8) {
            AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                ZStack {
                    Circle()
                        .fill(Color.purple.opacity(0.2))

                    Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                        .font(.title2)
                }
            }
            .frame(width: 60, height: 60)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(selectedPet?.id == pet.id ? Color.purple : Color.clear, lineWidth: 3)
            )

            Text(pet.name)
                .font(.petCaption)
                .fontWeight(.semibold)
                .foregroundColor(selectedPet?.id == pet.id ? .purple : .primary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(selectedPet?.id == pet.id ? Color.purple.opacity(0.1) : Color(.systemGray6))
        )
        .onTapGesture {
            selectedPet = pet
        }
    }

    private var memoryTypeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Memory Type")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            Picker("Memory Type", selection: $memoryType) {
                ForEach(MemoryType.allCases, id: \.self) { type in
                    Text(type.rawValue).tag(type)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }

    private var mediaSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(memoryType == .photo ? "Select Photo" : "Select Video")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            if let memoryImage = memoryImage {
                Image(uiImage: memoryImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 200)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .shadow(radius: 5)
            } else {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
                    .frame(height: 200)
                    .overlay(
                        VStack {
                            Image(systemName: memoryType == .photo ? "photo.fill" : "video.fill")
                                .font(.system(size: 40))
                                .foregroundColor(.purple)

                            Text(memoryType == .photo ? "Add Photo" : "Add Video")
                                .font(.petSubheadline)
                                .foregroundColor(.purple)
                        }
                    )
            }

            PhotosPicker(selection: $selectedPhoto, matching: memoryType == .photo ? .images : .videos) {
                Text(memoryImage == nil ? "Choose \(memoryType.rawValue)" : "Change \(memoryType.rawValue)")
                    .font(.petSubheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
            }
            .onChange(of: selectedPhoto) { _, newValue in
                Task {
                    if let data = try? await newValue?.loadTransferable(type: Data.self),
                       let image = UIImage(data: data) {
                        memoryImage = image
                    }
                }
            }
        }
    }

    private var memoryDetailsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Memory Details")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: 8) {
                Text("Title")
                    .font(.petSubheadline)
                    .fontWeight(.medium)

                TextField("Enter memory title", text: $memoryTitle)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }

            VStack(alignment: .leading, spacing: 8) {
                Text("Description")
                    .font(.petSubheadline)
                    .fontWeight(.medium)

                TextField("Describe this memory...", text: $memoryDescription, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3...6)
            }
        }
    }

    private var memoryOptionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Options")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                HStack {
                    Image(systemName: isPublic ? "globe.fill" : "lock.fill")
                        .foregroundColor(isPublic ? .blue : .gray)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Make Public")
                            .font(.petSubheadline)
                            .fontWeight(.medium)

                        Text("Share with the pet community")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Toggle("", isOn: $isPublic)
                }

                HStack {
                    Image(systemName: isFavorite ? "heart.fill" : "heart")
                        .foregroundColor(isFavorite ? .red : .gray)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Mark as Favorite")
                            .font(.petSubheadline)
                            .fontWeight(.medium)

                        Text("Add to your favorites collection")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Toggle("", isOn: $isFavorite)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
    }

    private var isFormValid: Bool {
        selectedPet != nil && !memoryTitle.isEmpty
    }

    private func saveMemory() {
        // In a real app, this would save to the database
        // For now, just dismiss the view
        dismiss()
    }
}

// MARK: - Create Vault View

struct CreateVaultView: View {
    @EnvironmentObject var mockDataService: MockDataService
    @Environment(\.dismiss) private var dismiss

    @State private var vaultName = ""
    @State private var vaultDescription = ""
    @State private var selectedPets: Set<UUID> = []
    @State private var isLocked = false
    @State private var unlockDate = Date()

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Vault Details
                    vaultDetailsSection

                    // Pet Selection
                    petSelectionSection

                    // Vault Options
                    vaultOptionsSection
                }
                .padding()
            }
            .navigationTitle("Create Vault")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        createVault()
                    }
                    .disabled(!isFormValid)
                }
            }
        }
    }

    private var vaultDetailsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Vault Details")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: 8) {
                Text("Name")
                    .font(.petSubheadline)
                    .fontWeight(.medium)

                TextField("Enter vault name", text: $vaultName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }

            VStack(alignment: .leading, spacing: 8) {
                Text("Description")
                    .font(.petSubheadline)
                    .fontWeight(.medium)

                TextField("Describe this vault...", text: $vaultDescription, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3...6)
            }
        }
    }

    private var petSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Select Pets")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            if mockDataService.mockPets.isEmpty {
                Text("No pets available")
                    .font(.petSubheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
            } else {
                VStack(spacing: 8) {
                    ForEach(mockDataService.mockPets, id: \.id) { pet in
                        petSelectionRow(pet: pet)
                    }
                }
            }
        }
    }

    private func petSelectionRow(pet: Pet) -> some View {
        HStack {
            AsyncImage(url: URL(string: pet.profileImageURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                ZStack {
                    Circle()
                        .fill(Color.purple.opacity(0.2))

                    Text(pet.species == "dog" ? "🐕" : pet.species == "cat" ? "🐱" : "🐾")
                        .font(.title3)
                }
            }
            .frame(width: 40, height: 40)
            .clipShape(Circle())

            VStack(alignment: .leading, spacing: 2) {
                Text(pet.name)
                    .font(.petSubheadline)
                    .fontWeight(.medium)

                Text(pet.breed)
                    .font(.petCaption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Button(action: {
                if selectedPets.contains(pet.id) {
                    selectedPets.remove(pet.id)
                } else {
                    selectedPets.insert(pet.id)
                }
            }) {
                Image(systemName: selectedPets.contains(pet.id) ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(selectedPets.contains(pet.id) ? .purple : .gray)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(selectedPets.contains(pet.id) ? Color.purple.opacity(0.1) : Color(.systemGray6))
        )
    }

    private var vaultOptionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Vault Options")
                .font(.petSubheadline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                HStack {
                    Image(systemName: isLocked ? "lock.fill" : "lock.open.fill")
                        .foregroundColor(isLocked ? .orange : .green)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Time Lock Vault")
                            .font(.petSubheadline)
                            .fontWeight(.medium)

                        Text("Lock until a future date")
                            .font(.petCaption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Toggle("", isOn: $isLocked)
                }

                if isLocked {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Unlock Date")
                            .font(.petSubheadline)
                            .fontWeight(.medium)

                        DatePicker("", selection: $unlockDate, displayedComponents: .date)
                            .datePickerStyle(CompactDatePickerStyle())
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
    }

    private var isFormValid: Bool {
        !vaultName.isEmpty && !selectedPets.isEmpty
    }

    private func createVault() {
        // In a real app, this would save to the database
        // For now, just dismiss the view
        dismiss()
    }
}

// MARK: - AI Analysis Results View

struct AIAnalysisResultsView: View {
    let results: PetHealthAnalysisResult
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("AI Analysis Results for \(results.pet.name)")
                        .font(.petTitle2)
                        .fontWeight(.bold)
                        .padding()

                    Text("Comprehensive AI analysis results will be displayed here")
                        .font(.petSubheadline)
                        .foregroundColor(.secondary)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                }
                .padding()
            }
            .navigationTitle("AI Analysis")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
