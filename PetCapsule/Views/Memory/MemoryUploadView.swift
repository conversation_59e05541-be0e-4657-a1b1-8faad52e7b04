//
//  MemoryUploadView.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import PhotosUI
import AVFoundation

struct MemoryUploadView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    
    let pet: Pet
    
    @State private var selectedMemoryType: MemoryType = .photo
    @State private var title = ""
    @State private var content = ""
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var selectedImage: UIImage?
    @State private var isRecording = false
    @State private var audioRecorder: AVAudioRecorder?
    @State private var recordingURL: URL?
    @State private var isUploading = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Spacing.lg) {
                    // Memory Type Selector
                    memoryTypeSelector
                    
                    // Title Input
                    VStack(alignment: .leading, spacing: Spacing.sm) {
                        Text("Title")
                            .font(.petHeadline)
                            .foregroundColor(.petText)
                        
                        TextField("Give this memory a title...", text: $title)
                            .petTextFieldStyle()
                    }
                    
                    // Content based on type
                    contentSection
                    
                    // Description Input
                    VStack(alignment: .leading, spacing: Spacing.sm) {
                        Text("Description")
                            .font(.petHeadline)
                            .foregroundColor(.petText)
                        
                        TextField("Tell the story behind this memory...", text: $content, axis: .vertical)
                            .lineLimit(3...6)
                            .petTextFieldStyle()
                    }
                    
                    Spacer(minLength: Spacing.xl)
                }
                .padding(Spacing.lg)
            }
            .navigationTitle("New Memory")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveMemory()
                    }
                    .disabled(!canSave || isUploading)
                }
            }
        }
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
    }
    
    private var memoryTypeSelector: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Memory Type")
                .font(.petHeadline)
                .foregroundColor(.petText)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: Spacing.md) {
                    ForEach(MemoryType.allCases, id: \.self) { type in
                        Button(action: {
                            selectedMemoryType = type
                            resetContent()
                        }) {
                            VStack(spacing: Spacing.sm) {
                                Image(systemName: type.systemImage)
                                    .font(.title2)
                                
                                Text(type.displayName)
                                    .font(.petCaption)
                            }
                            .padding(Spacing.md)
                            .background(
                                selectedMemoryType == type ? 
                                Color.petAccent.opacity(0.2) : 
                                Color.petSecondaryBackground
                            )
                            .foregroundColor(
                                selectedMemoryType == type ? 
                                Color.petAccent : 
                                Color.petText
                            )
                            .cornerRadius(CornerRadius.md)
                            .overlay(
                                RoundedRectangle(cornerRadius: CornerRadius.md)
                                    .stroke(
                                        selectedMemoryType == type ? 
                                        Color.petAccent : 
                                        Color.clear, 
                                        lineWidth: 2
                                    )
                            )
                        }
                    }
                }
                .padding(.horizontal, Spacing.lg)
            }
        }
    }
    
    @ViewBuilder
    private var contentSection: some View {
        switch selectedMemoryType {
        case .photo:
            photoSection
        case .video:
            videoSection
        case .audio:
            audioSection
        case .text, .milestone:
            EmptyView() // Text content is handled by the description field
        }
    }
    
    private var photoSection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Photo")
                .font(.petHeadline)
                .foregroundColor(.petText)
            
            PhotosPicker(
                selection: $selectedPhoto,
                matching: .images,
                photoLibrary: .shared()
            ) {
                if let selectedImage = selectedImage {
                    Image(uiImage: selectedImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 200)
                        .clipped()
                        .cornerRadius(CornerRadius.md)
                } else {
                    RoundedRectangle(cornerRadius: CornerRadius.md)
                        .fill(Color.petSecondaryBackground)
                        .frame(height: 200)
                        .overlay(
                            VStack(spacing: Spacing.sm) {
                                Image(systemName: "photo.badge.plus")
                                    .font(.title)
                                    .foregroundColor(.petAccent)
                                
                                Text("Tap to select photo")
                                    .font(.petCallout)
                                    .foregroundColor(.petSecondaryText)
                            }
                        )
                }
            }
            .onChange(of: selectedPhoto) { _, newValue in
                Task {
                    if let newValue = newValue {
                        if let data = try? await newValue.loadTransferable(type: Data.self) {
                            selectedImage = UIImage(data: data)
                        }
                    }
                }
            }
        }
    }
    
    private var videoSection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Video")
                .font(.petHeadline)
                .foregroundColor(.petText)
            
            RoundedRectangle(cornerRadius: CornerRadius.md)
                .fill(Color.petSecondaryBackground)
                .frame(height: 200)
                .overlay(
                    VStack(spacing: Spacing.sm) {
                        Image(systemName: "video.badge.plus")
                            .font(.title)
                            .foregroundColor(.petAccent)
                        
                        Text("Video upload coming soon")
                            .font(.petCallout)
                            .foregroundColor(.petSecondaryText)
                    }
                )
        }
    }
    
    private var audioSection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Audio Recording")
                .font(.petHeadline)
                .foregroundColor(.petText)
            
            VStack(spacing: Spacing.md) {
                Button(action: toggleRecording) {
                    HStack {
                        Image(systemName: isRecording ? "stop.circle.fill" : "mic.circle.fill")
                            .font(.title)
                        
                        Text(isRecording ? "Stop Recording" : "Start Recording")
                            .font(.petHeadline)
                    }
                    .foregroundColor(isRecording ? .petError : .petAccent)
                }
                .petButtonStyle(isRecording ? .destructive : .primary)
                
                if recordingURL != nil {
                    Text("Recording saved")
                        .font(.petCallout)
                        .foregroundColor(.petSuccess)
                }
            }
            .padding(Spacing.lg)
            .background(Color.petSecondaryBackground)
            .cornerRadius(CornerRadius.md)
        }
    }
    
    private var canSave: Bool {
        !title.isEmpty && 
        !content.isEmpty &&
        (selectedMemoryType != .photo || selectedImage != nil) &&
        (selectedMemoryType != .audio || recordingURL != nil)
    }
    
    private func resetContent() {
        selectedPhoto = nil
        selectedImage = nil
        recordingURL = nil
        if isRecording {
            stopRecording()
        }
    }
    
    private func toggleRecording() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }
    
    private func startRecording() {
        // Request microphone permission and start recording
        AVAudioSession.sharedInstance().requestRecordPermission { granted in
            if granted {
                DispatchQueue.main.async {
                    self.isRecording = true
                    // TODO: Implement actual audio recording
                    // This is a placeholder - you would implement AVAudioRecorder here
                }
            }
        }
    }
    
    private func stopRecording() {
        isRecording = false
        // TODO: Stop actual recording and save file
        // For now, create a placeholder URL
        recordingURL = URL(fileURLWithPath: "placeholder_recording.m4a")
    }
    
    private func saveMemory() {
        isUploading = true
        
        // Create new memory
        let newMemory = Memory(
            title: title,
            content: content,
            type: selectedMemoryType,
            mediaURL: nil, // TODO: Upload media and get URL
            tags: [] // TODO: Generate tags with AI
        )
        
        // Add to pet's memories
        pet.memories.append(newMemory)
        
        // Save to context
        modelContext.insert(newMemory)
        
        do {
            try modelContext.save()
            dismiss()
        } catch {
            errorMessage = "Failed to save memory: \(error.localizedDescription)"
            showingError = true
        }
        
        isUploading = false
    }
}

#Preview {
    MemoryUploadView(pet: Pet(
        name: "Buddy",
        breed: "Golden Retriever",
        age: 3,
        ownerID: "preview-user"
    ))
    .environmentObject(SupabaseService.shared)
}
