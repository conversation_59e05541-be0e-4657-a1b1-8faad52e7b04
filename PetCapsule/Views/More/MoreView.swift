//
//  MoreView.swift
//  PetCapsule
//
//  More page with Settings, Premium, and Pet Health
//

import SwiftUI

struct MoreView: View {
    @EnvironmentObject private var authService: AuthenticationService
    @EnvironmentObject private var subscriptionService: SubscriptionService
    @State private var showSettings = false
    @State private var showPremium = false
    @State private var showPetHealth = false
    @State private var showProfile = false
    @State private var animateItems = false

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    // User Profile Section
                    userProfileSection

                    // Main Features
                    mainFeaturesSection

                    // Account & Support
                    accountSupportSection

                    // App Information
                    appInfoSection
                }
                .padding(.horizontal)
                .padding(.bottom, 100)
            }
            .navigationTitle("More")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1)) {
                    animateItems = true
                }
            }
        }
        .sheet(isPresented: $showSettings) {
            AppSettingsView()
                .environmentObject(authService)
        }
        .sheet(isPresented: $showPremium) {
            PremiumHubView()
                .environmentObject(subscriptionService)
        }
        .sheet(isPresented: $showPetHealth) {
            PetHealthView()
                .environmentObject(authService)
        }
        .sheet(isPresented: $showProfile) {
            UserProfileView()
                .environmentObject(authService)
        }
    }

    // MARK: - User Profile Section

    private var userProfileSection: some View {
        VStack(spacing: 16) {
            Button(action: { showProfile = true }) {
                HStack(spacing: 16) {
                    // Profile Image
                    Circle()
                        .fill(LinearGradient(
                            gradient: Gradient(colors: [.purple, .blue]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .frame(width: 60, height: 60)
                        .overlay(
                            Text(getUserInitials())
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )

                    VStack(alignment: .leading, spacing: 4) {
                        Text(getUserName())
                            .font(.petTitle3)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        HStack {
                            Image(systemName: "crown.fill")
                                .foregroundColor(.purple)
                                .font(.caption)

                            Text(subscriptionService.subscriptionStatus.displayName)
                                .font(.petSubheadline)
                                .foregroundColor(.secondary)
                        }
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .scaleEffect(animateItems ? 1.0 : 0.9)
        .opacity(animateItems ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateItems)
    }

    // MARK: - Main Features Section

    private var mainFeaturesSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Features")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                Spacer()
            }

            VStack(spacing: 12) {
                moreMenuItem(
                    icon: "brain.head.profile.fill",
                    title: "Pet Health",
                    subtitle: "AI-powered health insights and analysis",
                    color: .purple,
                    action: { showPetHealth = true }
                )

                moreMenuItem(
                    icon: "crown.fill",
                    title: "Premium Features",
                    subtitle: "Unlock advanced AI and premium tools",
                    color: .orange,
                    badge: subscriptionService.subscriptionStatus == .free ? "Upgrade" : nil,
                    action: { showPremium = true }
                )

                moreMenuItem(
                    icon: "gearshape.fill",
                    title: "Settings",
                    subtitle: "App preferences and notifications",
                    color: .gray,
                    action: { showSettings = true }
                )
            }
        }
        .scaleEffect(animateItems ? 1.0 : 0.9)
        .opacity(animateItems ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateItems)
    }

    // MARK: - Account & Support Section

    private var accountSupportSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Account & Support")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                Spacer()
            }

            VStack(spacing: 12) {
                moreMenuItem(
                    icon: "questionmark.circle.fill",
                    title: "Help & Support",
                    subtitle: "Get help and contact support",
                    color: .blue,
                    action: { /* TODO: Implement help */ }
                )

                moreMenuItem(
                    icon: "star.fill",
                    title: "Rate PetTime Capsule",
                    subtitle: "Share your experience on the App Store",
                    color: .yellow,
                    action: { /* TODO: Implement rating */ }
                )

                moreMenuItem(
                    icon: "square.and.arrow.up.fill",
                    title: "Share App",
                    subtitle: "Tell your friends about PetTime Capsule",
                    color: .green,
                    action: { /* TODO: Implement sharing */ }
                )
            }
        }
        .scaleEffect(animateItems ? 1.0 : 0.9)
        .opacity(animateItems ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateItems)
    }

    // MARK: - App Information Section

    private var appInfoSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("About")
                    .font(.petTitle2)
                    .fontWeight(.bold)
                Spacer()
            }

            VStack(spacing: 12) {
                moreMenuItem(
                    icon: "doc.text.fill",
                    title: "Privacy Policy",
                    subtitle: "How we protect your data",
                    color: .indigo,
                    action: { /* TODO: Implement privacy policy */ }
                )

                moreMenuItem(
                    icon: "doc.plaintext.fill",
                    title: "Terms of Service",
                    subtitle: "Terms and conditions",
                    color: .teal,
                    action: { /* TODO: Implement terms */ }
                )

                HStack {
                    Text("Version 1.0.0")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("© 2024 PetTime Capsule")
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 8)
            }
        }
        .scaleEffect(animateItems ? 1.0 : 0.9)
        .opacity(animateItems ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateItems)
    }

    // MARK: - Helper Methods

    private func getUserName() -> String {
        return authService.currentUser?.displayName ?? "Pet Parent"
    }

    private func getUserInitials() -> String {
        let name = getUserName()
        let components = name.components(separatedBy: " ")
        if components.count >= 2 {
            return String(components[0].prefix(1)) + String(components[1].prefix(1))
        } else {
            return String(name.prefix(2))
        }
    }

    private func moreMenuItem(
        icon: String,
        title: String,
        subtitle: String,
        color: Color,
        badge: String? = nil,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Icon
                RoundedRectangle(cornerRadius: 12)
                    .fill(color.opacity(0.15))
                    .frame(width: 44, height: 44)
                    .overlay(
                        Image(systemName: icon)
                            .font(.title3)
                            .foregroundColor(color)
                    )

                // Content
                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        Text(title)
                            .font(.petSubheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)

                        if let badge = badge {
                            Text(badge)
                                .font(.petCaption2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(
                                    Capsule()
                                        .fill(Color.purple)
                                )
                        }

                        Spacer()
                    }

                    Text(subtitle)
                        .font(.petCaption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }

                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Subscription Status Extension

extension SubscriptionStatus {
    var displayName: String {
        switch self {
        case .free:
            return "Free Plan"
        case .premium:
            return "Premium Member"
        case .family:
            return "Family Plan"
        case .professional:
            return "Professional"
        case .expired:
            return "Expired Plan"
        }
    }
}

#Preview {
    MoreView()
        .environmentObject(AuthenticationService())
        .environmentObject(SubscriptionService.shared)
}
