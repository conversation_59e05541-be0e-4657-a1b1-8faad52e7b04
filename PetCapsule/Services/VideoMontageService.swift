//
//  VideoMontageService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import AVFoundation
import UIKit
import SwiftUI

class VideoMontageService: ObservableObject {
    static let shared = VideoMontageService()
    
    @Published var isGenerating = false
    @Published var progress: Double = 0.0
    @Published var currentStep = ""
    
    private init() {}
    
    // MARK: - Montage Generation
    
    func generateMontage(
        from memories: [Memory],
        theme: MontageTheme,
        duration: TimeInterval = 60
    ) async throws -> URL {
        await MainActor.run {
            isGenerating = true
            progress = 0.0
            currentStep = "Analyzing memories..."
        }
        
        // Step 1: Get AI script
        let script = try await AIService.shared.generateMontageScript(for: memories, theme: theme)
        
        await MainActor.run {
            progress = 0.2
            currentStep = "Preparing media assets..."
        }
        
        // Step 2: Prepare media assets
        let mediaAssets = try await prepareMediaAssets(for: memories)
        
        await MainActor.run {
            progress = 0.4
            currentStep = "Creating video composition..."
        }
        
        // Step 3: Create video composition
        let composition = try await createVideoComposition(
            assets: mediaAssets,
            script: script,
            duration: duration
        )
        
        await MainActor.run {
            progress = 0.7
            currentStep = "Adding transitions and effects..."
        }
        
        // Step 4: Add transitions and effects
        let enhancedComposition = try await addTransitionsAndEffects(
            to: composition,
            script: script
        )
        
        await MainActor.run {
            progress = 0.9
            currentStep = "Exporting video..."
        }
        
        // Step 5: Export final video
        let outputURL = try await exportVideo(composition: enhancedComposition)
        
        await MainActor.run {
            progress = 1.0
            currentStep = "Complete!"
            isGenerating = false
        }
        
        return outputURL
    }
    
    // MARK: - Media Asset Preparation
    
    private func prepareMediaAssets(for memories: [Memory]) async throws -> [MediaAsset] {
        var assets: [MediaAsset] = []
        
        for memory in memories {
            switch memory.type {
            case .photo:
                if let imageURL = memory.mediaURL {
                    let asset = try await createImageAsset(from: imageURL, memory: memory)
                    assets.append(asset)
                }
            case .video:
                if let videoURL = memory.mediaURL {
                    let asset = try await createVideoAsset(from: videoURL, memory: memory)
                    assets.append(asset)
                }
            case .text, .milestone:
                let asset = try await createTextAsset(from: memory)
                assets.append(asset)
            case .audio:
                // Audio memories can be represented with waveform visualization
                let asset = try await createAudioVisualizationAsset(from: memory)
                assets.append(asset)
            }
        }
        
        return assets
    }
    
    private func createImageAsset(from urlString: String, memory: Memory) async throws -> MediaAsset {
        guard let url = URL(string: urlString) else {
            throw VideoMontageError.invalidURL
        }
        
        // Download or load image
        let (data, _) = try await URLSession.shared.data(from: url)
        guard let image = UIImage(data: data) else {
            throw VideoMontageError.invalidImage
        }
        
        return MediaAsset(
            id: memory.id,
            type: .image,
            image: image,
            duration: 3.0, // Default 3 seconds per image
            title: memory.title,
            content: memory.content
        )
    }
    
    private func createVideoAsset(from urlString: String, memory: Memory) async throws -> MediaAsset {
        guard let url = URL(string: urlString) else {
            throw VideoMontageError.invalidURL
        }
        
        let asset = AVAsset(url: url)
        let duration = try await asset.load(.duration)
        
        return MediaAsset(
            id: memory.id,
            type: .video,
            videoAsset: asset,
            duration: min(CMTimeGetSeconds(duration), 10.0), // Max 10 seconds per video
            title: memory.title,
            content: memory.content
        )
    }
    
    private func createTextAsset(from memory: Memory) async throws -> MediaAsset {
        let textImage = try await generateTextImage(
            title: memory.title,
            content: memory.content
        )
        
        return MediaAsset(
            id: memory.id,
            type: .text,
            image: textImage,
            duration: 4.0, // 4 seconds for text
            title: memory.title,
            content: memory.content
        )
    }
    
    private func createAudioVisualizationAsset(from memory: Memory) async throws -> MediaAsset {
        // Create a waveform visualization for audio memories
        let waveformImage = try await generateWaveformImage(for: memory)
        
        return MediaAsset(
            id: memory.id,
            type: .audio,
            image: waveformImage,
            duration: 3.0,
            title: memory.title,
            content: memory.content
        )
    }
    
    // MARK: - Video Composition
    
    private func createVideoComposition(
        assets: [MediaAsset],
        script: MontageScript,
        duration: TimeInterval
    ) async throws -> AVMutableComposition {
        let composition = AVMutableComposition()
        
        guard let videoTrack = composition.addMutableTrack(
            withMediaType: .video,
            preferredTrackID: kCMPersistentTrackID_Invalid
        ) else {
            throw VideoMontageError.compositionError
        }
        
        var currentTime = CMTime.zero
        
        for sequence in script.sequences {
            guard let asset = assets.first(where: { $0.id.uuidString == sequence.memoryId }) else {
                continue
            }
            
            let sequenceDuration = CMTime(seconds: sequence.duration, preferredTimescale: 600)
            
            switch asset.type {
            case .image, .text, .audio:
                // Create video track from image
                if let imageTrack = try await createVideoTrackFromImage(
                    asset.image!,
                    duration: sequenceDuration
                ) {
                    try videoTrack.insertTimeRange(
                        CMTimeRange(start: .zero, duration: sequenceDuration),
                        of: imageTrack,
                        at: currentTime
                    )
                }
            case .video:
                // Insert video asset
                if let assetVideoTrack = try await asset.videoAsset?.loadTracks(withMediaType: .video).first {
                    try videoTrack.insertTimeRange(
                        CMTimeRange(start: .zero, duration: sequenceDuration),
                        of: assetVideoTrack,
                        at: currentTime
                    )
                }
            }
            
            currentTime = CMTimeAdd(currentTime, sequenceDuration)
        }
        
        return composition
    }
    
    private func addTransitionsAndEffects(
        to composition: AVMutableComposition,
        script: MontageScript
    ) async throws -> AVMutableComposition {
        // Add video effects, transitions, and text overlays
        // This would involve creating CALayer animations and video effects
        
        // For now, return the composition as-is
        // In a full implementation, you would add:
        // - Fade transitions
        // - Zoom effects
        // - Text overlays
        // - Color corrections
        // - Ken Burns effect for photos
        
        return composition
    }
    
    private func exportVideo(composition: AVMutableComposition) async throws -> URL {
        let outputURL = FileManager.default.temporaryDirectory
            .appendingPathComponent("montage_\(UUID().uuidString).mp4")
        
        // Remove existing file if it exists
        try? FileManager.default.removeItem(at: outputURL)
        
        guard let exportSession = AVAssetExportSession(
            asset: composition,
            presetName: AVAssetExportPresetHighestQuality
        ) else {
            throw VideoMontageError.exportError
        }
        
        exportSession.outputURL = outputURL
        exportSession.outputFileType = .mp4
        
        await exportSession.export()
        
        if let error = exportSession.error {
            throw error
        }
        
        return outputURL
    }
    
    // MARK: - Helper Methods
    
    private func generateTextImage(title: String, content: String) async throws -> UIImage {
        let size = CGSize(width: 1080, height: 1920) // 9:16 aspect ratio
        
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async {
                let renderer = UIGraphicsImageRenderer(size: size)
                let image = renderer.image { context in
                    // Background gradient
                    let gradient = CAGradientLayer()
                    gradient.frame = CGRect(origin: .zero, size: size)
                    gradient.colors = [
                        UIColor(red: 0.9, green: 0.8, blue: 1.0, alpha: 1.0).cgColor,
                        UIColor(red: 0.8, green: 0.9, blue: 1.0, alpha: 1.0).cgColor
                    ]
                    gradient.render(in: context.cgContext)
                    
                    // Text styling
                    let titleAttributes: [NSAttributedString.Key: Any] = [
                        .font: UIFont.systemFont(ofSize: 48, weight: .bold),
                        .foregroundColor: UIColor.black,
                        .paragraphStyle: {
                            let style = NSMutableParagraphStyle()
                            style.alignment = .center
                            return style
                        }()
                    ]
                    
                    let contentAttributes: [NSAttributedString.Key: Any] = [
                        .font: UIFont.systemFont(ofSize: 32, weight: .medium),
                        .foregroundColor: UIColor.darkGray,
                        .paragraphStyle: {
                            let style = NSMutableParagraphStyle()
                            style.alignment = .center
                            style.lineSpacing = 8
                            return style
                        }()
                    ]
                    
                    // Draw text
                    let titleRect = CGRect(x: 100, y: 400, width: size.width - 200, height: 200)
                    title.draw(in: titleRect, withAttributes: titleAttributes)
                    
                    let contentRect = CGRect(x: 100, y: 650, width: size.width - 200, height: 800)
                    content.draw(in: contentRect, withAttributes: contentAttributes)
                }
                
                continuation.resume(returning: image)
            }
        }
    }
    
    private func generateWaveformImage(for memory: Memory) async throws -> UIImage {
        // Generate a simple waveform visualization
        let size = CGSize(width: 1080, height: 1920)
        
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async {
                let renderer = UIGraphicsImageRenderer(size: size)
                let image = renderer.image { context in
                    // Background
                    UIColor(red: 0.1, green: 0.1, blue: 0.2, alpha: 1.0).setFill()
                    context.fill(CGRect(origin: .zero, size: size))
                    
                    // Waveform bars
                    UIColor.white.setFill()
                    let barWidth: CGFloat = 8
                    let spacing: CGFloat = 4
                    let totalBars = Int((size.width - 200) / (barWidth + spacing))
                    
                    for i in 0..<totalBars {
                        let height = CGFloat.random(in: 50...400)
                        let x = 100 + CGFloat(i) * (barWidth + spacing)
                        let y = (size.height - height) / 2
                        
                        let rect = CGRect(x: x, y: y, width: barWidth, height: height)
                        context.fill(rect)
                    }
                    
                    // Title
                    let titleAttributes: [NSAttributedString.Key: Any] = [
                        .font: UIFont.systemFont(ofSize: 48, weight: .bold),
                        .foregroundColor: UIColor.white,
                        .paragraphStyle: {
                            let style = NSMutableParagraphStyle()
                            style.alignment = .center
                            return style
                        }()
                    ]
                    
                    let titleRect = CGRect(x: 100, y: 200, width: size.width - 200, height: 100)
                    memory.title.draw(in: titleRect, withAttributes: titleAttributes)
                }
                
                continuation.resume(returning: image)
            }
        }
    }
    
    private func createVideoTrackFromImage(_ image: UIImage, duration: CMTime) async throws -> AVAssetTrack? {
        // This would create a video track from a static image
        // Implementation would involve creating an AVMutableComposition with the image
        // For brevity, returning nil here
        return nil
    }
}

// MARK: - Data Models

struct MediaAsset {
    let id: UUID
    let type: MediaAssetType
    var image: UIImage?
    var videoAsset: AVAsset?
    let duration: TimeInterval
    let title: String
    let content: String
}

enum MediaAssetType {
    case image
    case video
    case text
    case audio
}

enum VideoMontageError: Error {
    case invalidURL
    case invalidImage
    case compositionError
    case exportError
    case noAssets
}
