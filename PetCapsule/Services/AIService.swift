//
//  AIService.swift
//  PetCapsule
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftUI

class AIService: ObservableObject {
    static let shared = AIService()

    private let apiKey = Config.Gemini.apiKey
    private let baseURL = "\(Config.Gemini.baseURL)/models/\(Config.Gemini.modelName):generateContent"

    private init() {}

    // MARK: - Memory Analysis

    func analyzeMemory(title: String, content: String, imageData: Data? = nil) async throws -> MemoryAnalysis {
        let prompt = """
        Analyze this pet memory and provide:
        1. Milestone detection (e.g., "First Birthday", "Learning Tricks", "Vet Visit")
        2. Sentiment analysis (joyful, nostalgic, playful, calm, etc.)
        3. Relevant tags (max 5)
        4. Suggested title improvement if needed

        Title: \(title)
        Content: \(content)

        Respond in JSON format:
        {
            "milestone": "detected milestone or null",
            "sentiment": "primary emotion",
            "tags": ["tag1", "tag2", "tag3"],
            "suggestedTitle": "improved title or original",
            "confidence": 0.95
        }
        """

        let response = try await callGeminiAPI(prompt: prompt, imageData: imageData)
        return try parseMemoryAnalysis(from: response)
    }

    // MARK: - Memory Prompts

    func generateMemoryPrompts(for pet: Pet) async throws -> [MemoryPrompt] {
        let prompt = """
        Generate 5 thoughtful memory prompts for a pet owner to capture memories of their \(pet.breed) named \(pet.name).
        Consider the pet's age (\(pet.age) years) and create prompts that encourage meaningful storytelling.

        Respond in JSON format:
        {
            "prompts": [
                {
                    "title": "Prompt title",
                    "description": "Detailed prompt description",
                    "category": "daily_life|milestone|behavior|bonding|health",
                    "icon": "SF Symbol name"
                }
            ]
        }
        """

        let response = try await callGeminiAPI(prompt: prompt)
        return try parseMemoryPrompts(from: response)
    }

    // MARK: - Comfort & Support

    func generateComfortMessage(for situation: ComfortSituation, petName: String) async throws -> ComfortMessage {
        let prompt = """
        Generate a compassionate, supportive message for a pet owner dealing with \(situation.rawValue).
        Pet name: \(petName)

        The message should be:
        - Empathetic and understanding
        - Offer practical comfort
        - Acknowledge their feelings
        - Be culturally sensitive
        - Include gentle suggestions for coping

        Respond in JSON format:
        {
            "message": "Main comfort message",
            "suggestions": ["suggestion1", "suggestion2", "suggestion3"],
            "resources": ["resource1", "resource2"]
        }
        """

        let response = try await callGeminiAPI(prompt: prompt)
        return try parseComfortMessage(from: response)
    }

    // MARK: - Video Montage Scripts

    func generateMontageScript(for memories: [Memory], theme: MontageTheme) async throws -> MontageScript {
        let memoryDescriptions = memories.map { memory in
            "Title: \(memory.title), Type: \(memory.type.rawValue), Content: \(memory.content)"
        }.joined(separator: "\n")

        let prompt = """
        Create a video montage script for theme: \(theme.rawValue)

        Available memories:
        \(memoryDescriptions)

        Generate:
        1. Sequence order for memories
        2. Transition suggestions
        3. Music mood recommendations
        4. Text overlays for key moments
        5. Duration recommendations

        Respond in JSON format:
        {
            "title": "Montage title",
            "duration": 60,
            "musicMood": "upbeat|nostalgic|peaceful|emotional",
            "sequences": [
                {
                    "memoryId": "uuid",
                    "startTime": 0,
                    "duration": 5,
                    "transition": "fade|slide|zoom",
                    "textOverlay": "optional text"
                }
            ]
        }
        """

        let response = try await callGeminiAPI(prompt: prompt)
        return try parseMontageScript(from: response)
    }

    // MARK: - Pet Behavior Insights

    func analyzePetBehavior(memories: [Memory]) async throws -> BehaviorInsights {
        let behaviorData = memories.map { memory in
            "Date: \(memory.createdAt), Content: \(memory.content), Tags: \(memory.tags.joined(separator: ", "))"
        }.joined(separator: "\n")

        let prompt = """
        Analyze pet behavior patterns from these memories:

        \(behaviorData)

        Provide insights on:
        1. Personality traits
        2. Favorite activities
        3. Behavioral changes over time
        4. Health indicators
        5. Bonding moments

        Respond in JSON format:
        {
            "personalityTraits": ["trait1", "trait2"],
            "favoriteActivities": ["activity1", "activity2"],
            "behaviorTrends": "overall trend description",
            "healthIndicators": "health-related observations",
            "bondingMoments": "key bonding insights"
        }
        """

        let response = try await callGeminiAPI(prompt: prompt)
        return try parseBehaviorInsights(from: response)
    }

    // MARK: - General AI Response

    func generateResponse(prompt: String) async throws -> String {
        return try await callGeminiAPI(prompt: prompt)
    }

    // MARK: - Photo Book Generation

    func generatePhotoBookPages(
        memories: [Memory],
        template: Any, // Using Any to avoid type conflicts
        customization: Any // Using Any to avoid type conflicts
    ) async throws -> [Any] {
        // Simulate photo book page generation
        // In production, this would use AI to create optimal layouts
        return []
    }

    // MARK: - Private API Methods

    private func callGeminiAPI(prompt: String, imageData: Data? = nil) async throws -> String {
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw AIServiceError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 1024
            ]
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw AIServiceError.apiError
        }

        let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        guard let candidates = jsonResponse?["candidates"] as? [[String: Any]],
              let firstCandidate = candidates.first,
              let content = firstCandidate["content"] as? [String: Any],
              let parts = content["parts"] as? [[String: Any]],
              let firstPart = parts.first,
              let text = firstPart["text"] as? String else {
            throw AIServiceError.invalidResponse
        }

        return text
    }

    // MARK: - Response Parsing

    private func parseMemoryAnalysis(from response: String) throws -> MemoryAnalysis {
        guard let data = response.data(using: .utf8) else {
            throw AIServiceError.invalidResponse
        }
        return try JSONDecoder().decode(MemoryAnalysis.self, from: data)
    }

    private func parseMemoryPrompts(from response: String) throws -> [MemoryPrompt] {
        guard let data = response.data(using: .utf8) else {
            throw AIServiceError.invalidResponse
        }
        let container = try JSONDecoder().decode(MemoryPromptsResponse.self, from: data)
        return container.prompts
    }

    private func parseComfortMessage(from response: String) throws -> ComfortMessage {
        guard let data = response.data(using: .utf8) else {
            throw AIServiceError.invalidResponse
        }
        return try JSONDecoder().decode(ComfortMessage.self, from: data)
    }

    private func parseMontageScript(from response: String) throws -> MontageScript {
        guard let data = response.data(using: .utf8) else {
            throw AIServiceError.invalidResponse
        }
        return try JSONDecoder().decode(MontageScript.self, from: data)
    }

    private func parseBehaviorInsights(from response: String) throws -> BehaviorInsights {
        guard let data = response.data(using: .utf8) else {
            throw AIServiceError.invalidResponse
        }
        return try JSONDecoder().decode(BehaviorInsights.self, from: data)
    }
}

// MARK: - Data Models

struct MemoryAnalysis: Codable {
    let milestone: String?
    let sentiment: String
    let tags: [String]
    let suggestedTitle: String
    let confidence: Double
}

struct MemoryPrompt: Codable {
    let title: String
    let description: String
    let category: String
    let icon: String
}

struct MemoryPromptsResponse: Codable {
    let prompts: [MemoryPrompt]
}

struct ComfortMessage: Codable {
    let message: String
    let suggestions: [String]
    let resources: [String]
}

struct MontageScript: Codable {
    let title: String
    let duration: Int
    let musicMood: String
    let sequences: [MontageSequence]
}

struct MontageSequence: Codable {
    let memoryId: String
    let startTime: Double
    let duration: Double
    let transition: String
    let textOverlay: String?
}

struct BehaviorInsights: Codable {
    let personalityTraits: [String]
    let favoriteActivities: [String]
    let behaviorTrends: String
    let healthIndicators: String
    let bondingMoments: String
}

enum ComfortSituation: String, CaseIterable {
    case illness = "pet illness"
    case aging = "pet aging"
    case loss = "pet loss"
    case separation = "separation anxiety"
    case behavior = "behavioral issues"
    case vet = "vet visit stress"
}

enum MontageTheme: String, CaseIterable {
    case birthday = "birthday celebration"
    case year = "year in review"
    case growth = "puppy to adult"
    case adventures = "adventures together"
    case memorial = "memorial tribute"
    case milestones = "major milestones"
}

enum AIServiceError: Error {
    case invalidURL
    case apiError
    case invalidResponse
    case networkError
}

// Note: Physical product types are defined in PhysicalProductsService.swift
